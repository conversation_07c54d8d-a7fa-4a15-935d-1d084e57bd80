import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { CmdPatternConst } from '../../../../shared-modules';
import { OrgchartClient } from '../../mgs-sender/orgchart.client';
import { IOrgchartQueryDocument } from '../interfaces/document.interface';
import { CommonConst } from '../../shared/constant/common.const';
import { EmployeeClient } from '../../mgs-sender/employee.client';
import { EmployeeQueryRepository } from '../../employee/repository/query.repository';
import * as _ from 'lodash';

@Injectable()
export class OrgchartQueryRepository {

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IOrgchartQueryDocument>,
    private readonly empRepository: EmployeeQueryRepository,
    private readonly orgchartClient: OrgchartClient,
    private readonly employeeClient: EmployeeClient
  ) { }

  async prepareData(query) {
    const { isSync = 0 } = query;
    const lastSyncOrg = await this.readModel.find().sort({ lastSync: 'desc' }).limit(1);
    const lastSync = lastSyncOrg.length > 0 ? lastSyncOrg[0]['lastSync'] : null;
    const lastSyncDate = lastSync instanceof Date ? lastSync : null;

    const today = new Date();
    if (isSync == 1 || (lastSyncDate === null || today.getDate() != lastSyncDate.getDate() ||
      today.getMonth() != lastSyncDate.getMonth() ||
      today.getFullYear() != lastSyncDate.getFullYear())) {
      const orgcharts = await this.orgchartClient.sendDataPromise({ query }, CmdPatternConst.ORGCHART.LISTENER.GET_DROPDOWN);
      const orgCount = await this.readModel.countDocuments();
      if (orgcharts.length != orgCount) {
        const updates = orgcharts.map((org) => {
          if (org.id) {
            return {
              updateOne: {
                filter: { id: org.id },
                update: { $set: { ...org, name: org.name || org.nameVN, lastSync: today } },
                upsert: true
              }
            };
          }

          return null;
        }).filter(Boolean);

        if (updates.length > 0) {
          await this.readModel.bulkWrite(updates);
        } else {
          console.log("No updates org to perform.");
        }
      }
    }


    await this.empRepository.bulkWriteDropdown(query);
  }

  async getOrgchartById(id) {
    return await this.readModel.aggregate([
      {
        $match: { id }
      }, {
        $lookup: {
          from: 'employees',
          localField: 'staffIds',
          foreignField: 'id',
          as: 'emps'
        }
      }, {
        $addFields: {
          staffIds: {
            $map: {
              input: '$staffIds',
              as: 'staffId',
              in: {
                id: '$$staffId',
                info: {
                  $arrayElemAt: [{
                    $filter: {
                      input: '$emps',
                      cond: { $eq: ['$$this.id', '$$staffId'] }
                    }
                  }, 0]
                }
              }
            }
          }
        }
      }, {
        $project: {
          _id: 0,
          id: 1,
          name: 1,
          code: 1,
          staffIds: {
            $map: {
              input: '$staffIds',
              as: 'staff',
              in: {
                id: '$$staff.id',
                name: { $ifNull: ['$$staff.info.name', ''] },
                code: { $ifNull: ['$$staff.info.code', ''] },
                email: { $ifNull: ['$$staff.info.email', ''] }
              }
            }
          }
        }
      }
    ]).exec();
  }

  async getOrgchartByIds(ids) {
    return await this.readModel.aggregate([
      {
        $match: { id: { $in: ids } }
      }, {
        $lookup: {
          from: 'employees',
          localField: 'staffIds',
          foreignField: 'id',
          as: 'emps'
        }
      }, {
        $addFields: {
          staffIds: {
            $map: {
              input: '$staffIds',
              as: 'staffId',
              in: {
                id: '$$staffId',
                info: {
                  $arrayElemAt: [{
                    $filter: {
                      input: '$emps',
                      cond: { $eq: ['$$this.id', '$$staffId'] }
                    }
                  }, 0]
                }
              }
            }
          }
        }
      }, {
        $project: {
          _id: 0,
          id: 1,
          name: 1,
          code: 1,
          staffIds: {
            $map: {
              input: '$staffIds',
              as: 'staff',
              in: {
                id: '$$staff.id',
                name: { $ifNull: ['$$staff.info.name', ''] },
                code: { $ifNull: ['$$staff.info.code', ''] },
                email: { $ifNull: ['$$staff.info.email', ''] }
              }
            }
          }
        }
      }
    ]).exec();
  }

  async getDropdown(query) {
    // const orgcharts = await this.orgchartClient.sendDataPromise({ query }, CmdPatternConst.ORGCHART.LISTENER.GET_DROPDOWN);
    // const orgCount = await this.readModel.countDocuments();

    // if (orgcharts.length != orgCount) {
    //   await this.readModel.bulkWrite(
    //     orgcharts.map((org) => {
    //       if (org.id) {
    //         return {
    //           updateOne: {
    //             filter: { id: org.id },
    //             update: { $set: org },
    //             upsert: true
    //           }
    //         };
    //       }
    //     }).filter(Boolean)
    //   );
    // }

    // const employees = await this.employeeClient.sendDataPromise({}, CmdPatternConst.EMPLOYEE.DROPDOWN);
    // await this.empRepository.bulkWrite(employees);

    const { page, pageSize, search = '', id = '' } = query;
    const limit = parseInt(pageSize) || 10;
    const skip = (parseInt(page) || 1) * limit - limit;
    const match = {};
    if (!_.isEmpty(search)) {
      match['$or'] = [
        { name: { $regex: new RegExp(search), $options: 'i' } },
        { email: { $regex: new RegExp(search), $options: 'i' } }
      ];
    }

    if (!_.isEmpty(id)) {
      match['id'] = { $in: id.split(',') };
    }

    let [rows, total] = await Promise.all([
      this.readModel.aggregate([
        {
          $match: match
        }, {
          $lookup: {
            from: 'employees',
            localField: 'staffIds',
            foreignField: 'id',
            as: 'emps'
          }
        }, {
          $addFields: {
            staffIds: {
              $map: {
                input: '$staffIds',
                as: 'staffId',
                in: {
                  id: '$$staffId',
                  info: {
                    $arrayElemAt: [{
                      $filter: {
                        input: '$emps',
                        cond: { $eq: ['$$this.id', '$$staffId'] }
                      }
                    }, 0]
                  }
                }
              }
            }
          }
        }, {
          $skip: skip
        }, {
          $limit: limit
        }, {
          $project: {
            _id: 0,
            id: 1,
            name: 1,
            code: 1,
            staffIds: {
              $map: {
                input: '$staffIds',
                as: 'staff',
                in: {
                  id: '$$staff.id',
                  name: { $ifNull: ['$$staff.info.name', ''] },
                  code: { $ifNull: ['$$staff.info.code', ''] },
                  email: { $ifNull: ['$$staff.info.email', ''] }
                }
              }
            }
          }
        }
      ]),
      this.readModel.countDocuments(match),
    ]);

    return {
      rows,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      total,
      totalPages: Math.ceil(total / parseInt(pageSize)),
    };
  }
}
