import { IsInt, IsOptional, IsString } from 'class-validator';

export class GetCallLogsDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @IsString()
  from_number?: string;

  @IsOptional()
  @IsString()
  to_number?: string;

  @IsOptional()
  @IsString()
  from_user_id?: string;

  @IsOptional()
  @IsInt()
  from_start_time?: number;

  @IsOptional()
  @IsInt()
  to_start_time?: number;

  @IsOptional()
  @IsInt()
  from_answer_time?: number;

  @IsOptional()
  @IsInt()
  to_answer_time?: number;

  @IsOptional()
  @IsInt()
  from_stop_time?: number;

  @IsOptional()
  @IsInt()
  to_stop_time?: number;

  @IsOptional()
  @IsInt()
  from_created_time?: number;

  @IsOptional()
  @IsInt()
  to_created_time?: number;

  @IsOptional()
  @IsInt()
  page?: number;

  @IsOptional()
  @IsInt()
  limit?: number;

  @IsOptional()
  @IsString()
  sort_by?: string;

  @IsOptional()
  @IsString()
  order_by?: string;
}
