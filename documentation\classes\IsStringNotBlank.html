<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>IsStringNotBlank</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/classes/class-validation.ts</code>
        </p>



            <p class="comment">
                <h3>Implements</h3>
            </p>
            <p class="comment">
                        <code>ValidatorConstraintInterface</code>
            </p>


            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#defaultMessage">defaultMessage</a>
                            </li>
                            <li>
                                <a href="#validate">validate</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>



            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="defaultMessage"></a>
                    <span class="name">
                        <b>
                            defaultMessage
                        </b>
                        <a href="#defaultMessage"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>defaultMessage(args: ValidationArguments)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="17"
                            class="link-to-prism">src/modules/shared/classes/class-validation.ts:17</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>args</td>
                                    <td>
                                            <code>ValidationArguments</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validate"></a>
                    <span class="name">
                        <b>
                            validate
                        </b>
                        <a href="#validate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>validate(text: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, args: ValidationArguments)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="10"
                            class="link-to-prism">src/modules/shared/classes/class-validation.ts:10</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>text</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>args</td>
                                    <td>
                                            <code>ValidationArguments</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ValidatorConstraint, ValidationArguments, ValidatorConstraintInterface } from &#x27;class-validator&#x27;;
import { isNullOrUndefined } from &#x27;util&#x27;;
import { ExploitEnum } from &#x27;../../shared/enum/exploit.enum&#x27;;
import {MaritalStatusEnum} from &#x27;../../shared/enum/marital-status.enum&#x27;;
import {SexEnum} from &#x27;../../shared/enum/sex.enum&#x27;;

@ValidatorConstraint({ name: &#x27;stringNotBlank&#x27;, async: false })
export class IsStringNotBlank implements ValidatorConstraintInterface {

    validate(text: string, args: ValidationArguments) {
        if(isNullOrUndefined(text)) {
            return false;
        }
        return text.trim().length &gt; 0; // validate String is not blank
    }

    defaultMessage(args: ValidationArguments) {
        return &#x27;Value is blank or empty&#x27;;
    }
}
@ValidatorConstraint({ name: &#x27;legalLeadStatus&#x27;, async: false })
export class IsLegalLeadStatus implements ValidatorConstraintInterface {

    validate(text: string, args: ValidationArguments) {
        if (![ExploitEnum.ASSIGN, ExploitEnum.NEW, ExploitEnum.CANCEL, ExploitEnum.DONE, ExploitEnum.PROCESSING, ExploitEnum.REASSIGN, ExploitEnum.RENEW].includes(text as any)) {
            return false;
        }
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return &#x60;Value must be ${ExploitEnum.ASSIGN}, ${ExploitEnum.NEW}, ${ExploitEnum.CANCEL}, ${ExploitEnum.DONE}, ${ExploitEnum.PROCESSING}, ${ExploitEnum.REASSIGN}, ${ExploitEnum.RENEW}&#x60;;
    }
}
@ValidatorConstraint({ name: &#x27;legalSex&#x27;, async: false })
export class IsLegalSex implements ValidatorConstraintInterface {

    validate(text: string, args: ValidationArguments) {
        if (!!text) {
            if (![SexEnum.FEMALE, SexEnum.MALE, SexEnum.OTHER].includes(text as any)) {
                return false;
            } 
            return true;
        }
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return &#x60;Value must be ${SexEnum.FEMALE}, ${SexEnum.MALE}, ${SexEnum.OTHER}&#x60;;
    }
}
@ValidatorConstraint({ name: &#x27;legalMaritalStatus&#x27;, async: false })
export class IsLegalMaritalStatus implements ValidatorConstraintInterface {

    validate(text: string, args: ValidationArguments) {
        if (!!text) {
            if (![MaritalStatusEnum.ALONE, MaritalStatusEnum.MARRIED, MaritalStatusEnum.OTHER].includes(text as any)) {
                return false;
            } 
            return true;
        }
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return &#x60;Value must be ${MaritalStatusEnum.ALONE}, ${MaritalStatusEnum.MARRIED}, ${MaritalStatusEnum.OTHER}&#x60;;
    }
}
@ValidatorConstraint({ name: &#x27;legalIdentify&#x27;, async: false })
export class IsLegalIdentify implements ValidatorConstraintInterface {

    validate(input: any, args: ValidationArguments) {
        let flag: boolean &#x3D; true;
        if (!!input) {
           Object.keys(input).every((key: string) &#x3D;&gt; {
               if (![&#x27;type&#x27;, &#x27;num&#x27;, &#x27;date&#x27;, &#x27;issueBy&#x27;].includes(key)) {
                flag &#x3D; false;
                return false;
               }
           });
           return flag;
        }
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return &#x60;Value must be a object with key includes: type, num, date, issueBy&#x60;;
    }
}

@ValidatorConstraint({ name: &#x27;legalSurvey&#x27;, async: false })
export class IsLegalSurvey implements ValidatorConstraintInterface {

    validate(input: any, args: ValidationArguments) {
        let flag: boolean &#x3D; true;
        if (!!input &amp;&amp; Array.isArray(input)) {
            input.every((item: object) &#x3D;&gt; {
                Object.keys(item).every((key: string) &#x3D;&gt; {
                    if (![&#x27;type&#x27;, &#x27;name&#x27;, &#x27;value&#x27;, &#x27;code&#x27;].includes(key)) {
                        flag &#x3D; false;
                        return false;
                       }
                });
            });
            return flag;
        } else flag &#x3D; false;
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return &#x60;Value must be a array object with key includes: type, name, value, code&#x60;;
    }
}</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'IsStringNotBlank.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
