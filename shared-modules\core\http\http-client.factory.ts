import { Injectable } from '@nestjs/common';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Observable } from 'rxjs';

// Interface chung cho HttpService
export interface IHttpClient {
  get<T>(url: string, config?: any): Observable<AxiosResponse<T>>;
  post<T, R>(url: string, body: T, config?: any): Observable<AxiosResponse<R>>;
  put<T, R>(url: string, body: T, config?: any): Observable<AxiosResponse<R>>;
  delete<T>(url: string, config?: any): Observable<AxiosResponse<T>>;
  request<T>(config: AxiosRequestConfig): Observable<AxiosResponse<T>>;
}

// Factory để tạo HttpClient phù hợp
@Injectable()
export class HttpClientFactory {
  static createHttpClient(httpService: any): IHttpClient {
    // Kiểm tra xem httpService có phương thức toPromise không
    // NestJS v6 (@nestjs/common) có phương thức này
    if (httpService.get && typeof httpService.get().toPromise === 'function') {
      return new NestJsV6HttpClient(httpService);
    }
    // NestJS v10 (@nestjs/axios) không có toPromise
    return new NestJsV10HttpClient(httpService);
  }
}

// Adapter cho NestJS v6
class NestJsV6HttpClient implements IHttpClient {
  constructor(private readonly httpService: any) { }

  get<T>(url: string, config?: any): Observable<AxiosResponse<T>> {
    return this.httpService.get(url, config);
  }

  post<T, R>(url: string, body: T, config?: any): Observable<AxiosResponse<R>> {
    return this.httpService.post(url, body, config);
  }

  put<T, R>(url: string, body: T, config?: any): Observable<AxiosResponse<R>> {
    return this.httpService.put(url, body, config);
  }

  delete<T>(url: string, config?: any): Observable<AxiosResponse<T>> {
    return this.httpService.delete(url, config);
  }

  request<T>(config: AxiosRequestConfig): Observable<AxiosResponse<T>> {
    return this.httpService.request(config);
  }
}

// Adapter cho NestJS v10
class NestJsV10HttpClient implements IHttpClient {
  constructor(private readonly httpService: any) { }

  get<T>(url: string, config?: any): Observable<AxiosResponse<T>> {
    return this.httpService.get(url, config);
  }

  post<T, R>(url: string, body: T, config?: any): Observable<AxiosResponse<R>> {
    return this.httpService.post(url, body, config);
  }

  put<T, R>(url: string, body: T, config?: any): Observable<AxiosResponse<R>> {
    return this.httpService.put(url, body, config);
  }

  delete<T>(url: string, config?: any): Observable<AxiosResponse<T>> {
    return this.httpService.delete(url, config);
  }

  request<T>(config: AxiosRequestConfig): Observable<AxiosResponse<T>> {
    return this.httpService.request(config);
  }
}
