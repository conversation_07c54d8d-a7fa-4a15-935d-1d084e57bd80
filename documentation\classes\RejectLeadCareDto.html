<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>RejectLeadCareDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/leadCare.domain/dto/leadCare.dto.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#id">id</a>
                            </li>
                            <li>
                                <a href="#reason">reason</a>
                            </li>
                            <li>
                                <a href="#status">status</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="id"></a>
                        <span class="name">
                            <b>
                            id</b>
                            <a href="#id"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="253" class="link-to-prism">src/modules/leadCare.domain/dto/leadCare.dto.ts:253</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="reason"></a>
                        <span class="name">
                            <b>
                            reason</b>
                            <a href="#reason"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="251" class="link-to-prism">src/modules/leadCare.domain/dto/leadCare.dto.ts:251</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="status"></a>
                        <span class="name">
                            <b>
                            status</b>
                            <a href="#status"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="252" class="link-to-prism">src/modules/leadCare.domain/dto/leadCare.dto.ts:252</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { IsString, Validate, IsEnum, IsNotEmpty } from &#x27;class-validator&#x27;;
import { ClassBased } from &#x27;../../shared/classes/class-based&#x27;;
import {
    ILeadCare,
    IExploitHistory,
    ITakeCare,
    IProject,
} from &#x27;../../shared/services/leadCare/interfaces/leadCare.interface&#x27;;
import {
    IsStringNotBlank,
    IsLegalLeadStatus,
    IsLegalSex,
    IsLegalMaritalStatus,
    IsLegalIdentify,
    IsLegalSurvey,
} from &#x27;../../shared/classes/class-validation&#x27;;
import { ApiModelPropertyOptional, ApiModelProperty } from &#x27;@nestjs/swagger&#x27;;
import { Type } from &#x27;class-transformer&#x27;;
import { ExploitCareEnum } from &#x27;../../shared/enum/exploit.enum&#x27;;
import { LeadRepoCareEnum} from &quot;../../shared/enum/type.enum&quot;;

export class ExploitHistory implements IExploitHistory {
    @ApiModelPropertyOptional()
    status: ExploitCareEnum;

    @ApiModelPropertyOptional()
    updatedAt: Date;

    @ApiModelPropertyOptional()
    takeCareId?: string;

    @ApiModelPropertyOptional()
    updatedBy?: string;
}

export class LeadCareDto extends ClassBased implements ILeadCare {
    customerId: string;
    @ApiModelPropertyOptional()
    id: string;
    @ApiModelPropertyOptional()
    name: string;
    @ApiModelPropertyOptional()
    address: string;
    @ApiModelPropertyOptional()
    phone: string;
    @ApiModelPropertyOptional()
    pos: Object;
    @ApiModelPropertyOptional()
    status: string;
    @ApiModelPropertyOptional()
    processBy: string;
    @ApiModelPropertyOptional()
    type: string;
    @ApiModelPropertyOptional()
    description: string;
    @ApiModelPropertyOptional()
    createdDate: Date;
    @ApiModelPropertyOptional()
    updatedDate: Date;
    @ApiModelPropertyOptional()
    t0: Date;
    @ApiModelPropertyOptional()
    t1: Date;
    @ApiModelPropertyOptional()
    t2: Date;
    @ApiModelPropertyOptional()
    t3: Date;
    @ApiModelPropertyOptional()
    timeOut: Date;
    @ApiModelPropertyOptional()
    email: string;
    @ApiModelPropertyOptional()
    lifeCycleStatus: string;
    timezoneclient: string;
    notes: Object[];
    timestamp: number;
    source: string;
    customer: object;
    property: object;
    processedDate: Date;
    @ApiModelPropertyOptional()
    processedHistory: any[];
    @ApiModelPropertyOptional()
    code: string;
    @ApiModelPropertyOptional()
    isCalled: boolean;
    @ApiModelPropertyOptional()
    advisingType: string;
    assignedDate: Date;

    // new
    updatedName: string;
    updatedPhone: string;
    updatedEmail: string;

    isInNeed: string;
    reasonNoNeed: string;
    otherReason: string;
    interestedProduct: Object[];
    direction: Object[];
    needLoan: boolean;
    isAppointment: boolean;
    isVisited: boolean;
    note: string;

    callHistory: any[];

    callId: string;
    startCall: Date;
    endCall: Date;
    answerTime: number;

    importedBy: Object;

    // LeadCare repository feature &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;
    exploitStatus: ExploitCareEnum;
    exploitHistory: IExploitHistory[];
    exploitStatusModifiedBy: string;
    repoId: string;
    repoConfigCode: string;
    isHot: boolean;
    assignDuration: number;
    takeCare: ITakeCare;
    project: IProject;
}

export class CreateLeadCareDto extends LeadCareDto {
    @IsString()
    @IsNotEmpty()
    type: string;

    @IsString()
    // @IsNotEmpty()
    address: string;

    // @IsNotEmpty()
    pos: Object;

    recaptcha: string;

    name: string;

    customerId: string;

    timestamp: number;

    source: string;

    customer: object;
    employee: object;
    property: object;
    processedDate: Date;
    code: string; // auto generate code.
    images: Object;
    usedFloorArea: number;
}

export class CreateLeadCareAdvisingDto extends LeadCareDto {
    @IsString()
    @IsNotEmpty()
    type: string;
    address: string;
    pos: Object;
    recaptcha: string;
    name: string;
    customerId: string;
    timestamp: number;
    source: string;
    customer: object;
    employee: object;
    property: object;
    processedDate: Date;
    code: string; //auto generate code.
    images: Object;
    advisingType: string;
    usedFloorArea: number;
}
export class ImportLeadCareDemandDto {
    @IsString()
    @IsNotEmpty({message: &#x27;Vui lòng chọn công ty&#x27;})
    companyId: string;
    @IsString()
    // @IsNotEmpty({message: &#x27;Vui lòng chọn sàn giao dịch&#x27;})
    exchangeId: string;
    // @IsString()
    posId: string;
    @IsNotEmpty()
    timestamp: number;
    @IsString()
    @IsNotEmpty({message: &#x27;Vui lòng chọn nguồn&#x27;})
    resource: string;
}

export class AssignLeadCareDto {

    @ApiModelProperty()
    @IsString()
    @Validate(IsStringNotBlank)
    id: string;

    @ApiModelProperty()
    @IsString()
    @Validate(IsStringNotBlank)
    assignFor: string;
}

export class RequestDto {
    id: string;
    reason: string;
    notes: Object[];
}
export class CallHistoryDto {
    id: string;
    callId: string;
    startCall: Date;
    endCall: Date;
    answerTime: number;
}

export class RejectDto {
    id: string;
    causeReject: string[];
}

export class UpdateStatusDto{
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    @IsString()
    id: string;

    @ApiModelPropertyOptional()
    @IsNotEmpty()
    @IsString()
    @Validate(IsLegalLeadStatus)
    status: string;
}

export class UpdateStatusAssignDto {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  id: string

  @ApiModelPropertyOptional()
  takeCare: ITakeCare
  dateEndWork:Date
  note : String
}

export class RejectLeadCareDto {
    reason : string;
    status : string;
    id: string;
}

export class HandleTransferRequestDto {
    @IsNotEmpty()
    @IsString()
    id: string;

    @IsNotEmpty()
    @IsString()
    type : ExploitCareEnum;

    note : string;
}

export class TakeSurveyLeadCareDto {
    surveys : any;
}

export class ConversationDto {
    @IsNotEmpty()
    @IsString()
    id: string;

    conversation: any;
}

class SurveyObj{
    @ApiModelProperty ()
    type: string;
    @ApiModelProperty ()
    code: string;
    @ApiModelProperty ()
    value: string;
    @ApiModelProperty ()
    name: string;
  }
class IdentifyObj{
    @ApiModelProperty ()
    type: string;
    @ApiModelProperty ()
    num: string;
    @ApiModelProperty ()
    date: string;
    @ApiModelProperty ()
    issueBy: string;
  }
class AddrObj{
    @ApiModelProperty ()
    nation: string;
    @ApiModelProperty ()
    province: string;
    @ApiModelProperty ()
    district: string;
    @ApiModelProperty ()
    ward: string;
  }
export class UpdateLeadCareDto{
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    @IsString()
    id: string;

    @ApiModelPropertyOptional()
    @Validate(IsLegalSex)
    sex: string;

    @ApiModelPropertyOptional()
    dob: string;
    @ApiModelPropertyOptional()
    subPhone: string[];
    @ApiModelPropertyOptional()
    email: string;
    @ApiModelPropertyOptional({type: AddrObj})
    objAddress: Object;
    @ApiModelPropertyOptional()
    major: string;
    @ApiModelPropertyOptional()
    income: string;
    @ApiModelPropertyOptional()
    sourceIncome: string;
    @ApiModelPropertyOptional()
    assignDuration: string;

    @ApiModelPropertyOptional()
    @Validate(IsLegalMaritalStatus)
    maritalStatus: string;

    @ApiModelPropertyOptional({type:  [SurveyObj]})
    @Validate(IsLegalSurvey)
    surveys: Object[];

    @ApiModelPropertyOptional({type: IdentifyObj})
    @Validate(IsLegalIdentify)
    identification: Object;
}

export class ImportLeadCareFromPublicForm extends LeadCareDto {
    @IsNotEmpty()
    @ApiModelPropertyOptional()
    phone: string;

    @IsNotEmpty()
    @ApiModelPropertyOptional()
    name: string;

    @IsNotEmpty()
    @ApiModelPropertyOptional()
    repoId: string;

    @ApiModelPropertyOptional()
    repoConfigCode: string;

    @ApiModelPropertyOptional()
    isHot: boolean;
}

export class ImportLeadCareAsExcelDto {
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    repoId: string;

    @ApiModelPropertyOptional()
    @IsNotEmpty()
    repoConfigCode: string;

    @ApiModelPropertyOptional()
    source: string;
}
export class CreateServiceRequestDto{
  @IsNotEmpty()
  @IsString()
  projectId: string;

  @IsNotEmpty()
  @IsString()
  repoType: LeadRepoCareEnum;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  description: string;

  customData: object;
}

export class CreateServiceRequestPublicDto extends CreateServiceRequestDto {
  @IsNotEmpty()
  @IsString()
  email: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  phone: string;

  target: string;
}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'RejectLeadCareDto.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
