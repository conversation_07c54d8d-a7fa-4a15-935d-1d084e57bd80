<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>PermissionConst</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/constant/permission.const.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#ADMIN_GET_ALL_TRANSER_HISTORY">ADMIN_GET_ALL_TRANSER_HISTORY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#ASSGIN_LEAD">ASSGIN_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#ASSGIN_LEAD_CARE">ASSGIN_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CREATE_LEAD">CREATE_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CREATE_LEAD_CARE">CREATE_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DELETE_LEAD">DELETE_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DELETE_LEAD_CARE">DELETE_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#FAIL_LEAD">FAIL_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#FAIL_LEAD_CARE">FAIL_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ALL_LEAD">GET_ALL_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ALL_LEAD_ADVISING">GET_ALL_LEAD_ADVISING</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ALL_LEAD_ADVISING_CARE">GET_ALL_LEAD_ADVISING_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ALL_LEAD_BY_POS">GET_ALL_LEAD_BY_POS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ALL_LEAD_BY_POS_CARE">GET_ALL_LEAD_BY_POS_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ALL_LEAD_CARE">GET_ALL_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ALL_LEAD_HISTORY">GET_ALL_LEAD_HISTORY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ALL_LEAD_HISTORY_CARE">GET_ALL_LEAD_HISTORY_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_EVENTSTREAM">GET_EVENTSTREAM</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_EVENTSTREAM_CARE">GET_EVENTSTREAM_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ID_LEAD">GET_ID_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_ID_LEAD_CARE">GET_ID_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_LEAD_PROCESS_BY_EMPLOYEE">GET_LEAD_PROCESS_BY_EMPLOYEE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#GET_LEAD_PROCESS_BY_EMPLOYEE_CARE">GET_LEAD_PROCESS_BY_EMPLOYEE_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDLE_TRANSFER_REQUEST">HANDLE_TRANSFER_REQUEST</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#IMPORT_LEAD">IMPORT_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#IMPORT_LEAD_CARE">IMPORT_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_CARE_BQL_GET_ALL">LEAD_CARE_BQL_GET_ALL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_CARE_GET_ALL">LEAD_CARE_GET_ALL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_CARE_GET_BY_ADMIN">LEAD_CARE_GET_BY_ADMIN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_COMMON_VIEW_MENU">LEAD_COMMON_VIEW_MENU</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_COMMON_VIEW_MENU_CARE">LEAD_COMMON_VIEW_MENU_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_GET_ALL_COMPANY">LEAD_GET_ALL_COMPANY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_GET_ALL_SUPER_ADMIN">LEAD_GET_ALL_SUPER_ADMIN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_GET_ALL_UNIT">LEAD_GET_ALL_UNIT</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_MANUAL_DELIVER">LEAD_MANUAL_DELIVER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_PRIMARY_VIEW_MENU">LEAD_PRIMARY_VIEW_MENU</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_PRIMARY_VIEW_MENU_CARE">LEAD_PRIMARY_VIEW_MENU_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_BY_EMP">LEAD_REPORT_BY_EMP</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_BY_EMP_CARE">LEAD_REPORT_BY_EMP_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_BY_IMPORTER">LEAD_REPORT_BY_IMPORTER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_BY_IMPORTER_CARE">LEAD_REPORT_BY_IMPORTER_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_BY_PROJECT_CARE">LEAD_REPORT_BY_PROJECT_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_QUERY_ALL_CARE">LEAD_REPORT_QUERY_ALL_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_VIEW_ALL">LEAD_REPORT_VIEW_ALL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_VIEW_ALL_CARE">LEAD_REPORT_VIEW_ALL_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_VIEW_MENU">LEAD_REPORT_VIEW_MENU</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REPORT_VIEW_MENU_CARE">LEAD_REPORT_VIEW_MENU_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_REVOKE_DELIVER">LEAD_REVOKE_DELIVER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_VIEW_MENU">LEAD_VIEW_MENU</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEAD_VIEW_MENU_CARE">LEAD_VIEW_MENU_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_CREATE">LEADJOB_CREATE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_DELETE">LEADJOB_DELETE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_GET">LEADJOB_GET</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_GET_ALL">LEADJOB_GET_ALL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_GET_BY_ADMIN">LEADJOB_GET_BY_ADMIN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_GET_BY_POS">LEADJOB_GET_BY_POS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_GET_ID">LEADJOB_GET_ID</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_PROCESS">LEADJOB_PROCESS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_UPDATE">LEADJOB_UPDATE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_VIEW_MENU">LEADJOB_VIEW_MENU</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LEADJOB_VIEW_PLAN_MENU">LEADJOB_VIEW_PLAN_MENU</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#MANAGE_REPOSITORY">MANAGE_REPOSITORY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#MANAGE_REPOSITORY_CARE">MANAGE_REPOSITORY_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PENDING_LEAD">PENDING_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PENDING_LEAD_CARE">PENDING_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROCESS_LEAD">PROCESS_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROCESS_LEAD_CARE">PROCESS_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PULL_LEAD">PULL_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PULL_LEAD_CARE">PULL_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REJECT_LEAD">REJECT_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REJECT_LEAD_CARE">REJECT_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REPORT_EXPLOIT_LEAD">REPORT_EXPLOIT_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REPORT_EXPLOIT_LEAD_CARE">REPORT_EXPLOIT_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REPORT_LEAD">REPORT_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REPORT_LEAD_CARE">REPORT_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SERVICE">SERVICE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#UNPROCESS_LEAD">UNPROCESS_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#UNPROCESS_LEAD_CARE">UNPROCESS_LEAD_CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#UPDATE_LEAD">UPDATE_LEAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#UPDATE_LEAD_CARE">UPDATE_LEAD_CARE</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#ALL_ROLES">ALL_ROLES</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PERMISSION_PACKAGES">PERMISSION_PACKAGES</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ADMIN_GET_ALL_TRANSER_HISTORY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            ADMIN_GET_ALL_TRANSER_HISTORY</b>
                            <a href="#ADMIN_GET_ALL_TRANSER_HISTORY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;admin.get.all.transfer.history&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="76" class="link-to-prism">src/modules/shared/constant/permission.const.ts:76</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ASSGIN_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            ASSGIN_LEAD</b>
                            <a href="#ASSGIN_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.assign&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="12" class="link-to-prism">src/modules/shared/constant/permission.const.ts:12</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ASSGIN_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            ASSGIN_LEAD_CARE</b>
                            <a href="#ASSGIN_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.assign&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="27" class="link-to-prism">src/modules/shared/constant/permission.const.ts:27</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CREATE_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CREATE_LEAD</b>
                            <a href="#CREATE_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.create&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="3" class="link-to-prism">src/modules/shared/constant/permission.const.ts:3</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CREATE_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CREATE_LEAD_CARE</b>
                            <a href="#CREATE_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.create&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/modules/shared/constant/permission.const.ts:18</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DELETE_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DELETE_LEAD</b>
                            <a href="#DELETE_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.delete&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="5" class="link-to-prism">src/modules/shared/constant/permission.const.ts:5</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DELETE_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DELETE_LEAD_CARE</b>
                            <a href="#DELETE_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.delete&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="20" class="link-to-prism">src/modules/shared/constant/permission.const.ts:20</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="FAIL_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            FAIL_LEAD</b>
                            <a href="#FAIL_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.fail&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="8" class="link-to-prism">src/modules/shared/constant/permission.const.ts:8</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="FAIL_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            FAIL_LEAD_CARE</b>
                            <a href="#FAIL_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.fail&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="23" class="link-to-prism">src/modules/shared/constant/permission.const.ts:23</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ALL_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ALL_LEAD</b>
                            <a href="#GET_ALL_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.get.all.lead&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/modules/shared/constant/permission.const.ts:31</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ALL_LEAD_ADVISING"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ALL_LEAD_ADVISING</b>
                            <a href="#GET_ALL_LEAD_ADVISING"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.get.all.lead.advising&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/modules/shared/constant/permission.const.ts:36</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ALL_LEAD_ADVISING_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ALL_LEAD_ADVISING_CARE</b>
                            <a href="#GET_ALL_LEAD_ADVISING_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.get.all.lead.advising&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="57" class="link-to-prism">src/modules/shared/constant/permission.const.ts:57</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ALL_LEAD_BY_POS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ALL_LEAD_BY_POS</b>
                            <a href="#GET_ALL_LEAD_BY_POS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.get.all.by.pos&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="32" class="link-to-prism">src/modules/shared/constant/permission.const.ts:32</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ALL_LEAD_BY_POS_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ALL_LEAD_BY_POS_CARE</b>
                            <a href="#GET_ALL_LEAD_BY_POS_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.get.all.by.pos&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="53" class="link-to-prism">src/modules/shared/constant/permission.const.ts:53</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ALL_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ALL_LEAD_CARE</b>
                            <a href="#GET_ALL_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.get.all.lead&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="52" class="link-to-prism">src/modules/shared/constant/permission.const.ts:52</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ALL_LEAD_HISTORY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ALL_LEAD_HISTORY</b>
                            <a href="#GET_ALL_LEAD_HISTORY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.get.all.lead.history&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/modules/shared/constant/permission.const.ts:29</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ALL_LEAD_HISTORY_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ALL_LEAD_HISTORY_CARE</b>
                            <a href="#GET_ALL_LEAD_HISTORY_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.get.all.lead.history&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="50" class="link-to-prism">src/modules/shared/constant/permission.const.ts:50</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_EVENTSTREAM"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_EVENTSTREAM</b>
                            <a href="#GET_EVENTSTREAM"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.get.lead.eventstream&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="30" class="link-to-prism">src/modules/shared/constant/permission.const.ts:30</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_EVENTSTREAM_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_EVENTSTREAM_CARE</b>
                            <a href="#GET_EVENTSTREAM_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.get.lead.eventstream&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="51" class="link-to-prism">src/modules/shared/constant/permission.const.ts:51</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ID_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ID_LEAD</b>
                            <a href="#GET_ID_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.get.id&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="33" class="link-to-prism">src/modules/shared/constant/permission.const.ts:33</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_ID_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_ID_LEAD_CARE</b>
                            <a href="#GET_ID_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.get.id&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="54" class="link-to-prism">src/modules/shared/constant/permission.const.ts:54</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_LEAD_PROCESS_BY_EMPLOYEE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_LEAD_PROCESS_BY_EMPLOYEE</b>
                            <a href="#GET_LEAD_PROCESS_BY_EMPLOYEE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.get.process.by.employee&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="34" class="link-to-prism">src/modules/shared/constant/permission.const.ts:34</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="GET_LEAD_PROCESS_BY_EMPLOYEE_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            GET_LEAD_PROCESS_BY_EMPLOYEE_CARE</b>
                            <a href="#GET_LEAD_PROCESS_BY_EMPLOYEE_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.get.process.by.employee&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="55" class="link-to-prism">src/modules/shared/constant/permission.const.ts:55</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDLE_TRANSFER_REQUEST"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDLE_TRANSFER_REQUEST</b>
                            <a href="#HANDLE_TRANSFER_REQUEST"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;handle.transfer.request&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="15" class="link-to-prism">src/modules/shared/constant/permission.const.ts:15</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="IMPORT_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            IMPORT_LEAD</b>
                            <a href="#IMPORT_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.import&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/modules/shared/constant/permission.const.ts:35</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="IMPORT_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            IMPORT_LEAD_CARE</b>
                            <a href="#IMPORT_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.import&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="56" class="link-to-prism">src/modules/shared/constant/permission.const.ts:56</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_CARE_BQL_GET_ALL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_CARE_BQL_GET_ALL</b>
                            <a href="#LEAD_CARE_BQL_GET_ALL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.bql.get.all&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="72" class="link-to-prism">src/modules/shared/constant/permission.const.ts:72</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_CARE_GET_ALL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_CARE_GET_ALL</b>
                            <a href="#LEAD_CARE_GET_ALL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.get.all&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="70" class="link-to-prism">src/modules/shared/constant/permission.const.ts:70</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_CARE_GET_BY_ADMIN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_CARE_GET_BY_ADMIN</b>
                            <a href="#LEAD_CARE_GET_BY_ADMIN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.get.by.admin&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="71" class="link-to-prism">src/modules/shared/constant/permission.const.ts:71</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_COMMON_VIEW_MENU"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_COMMON_VIEW_MENU</b>
                            <a href="#LEAD_COMMON_VIEW_MENU"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.common.view.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="41" class="link-to-prism">src/modules/shared/constant/permission.const.ts:41</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_COMMON_VIEW_MENU_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_COMMON_VIEW_MENU_CARE</b>
                            <a href="#LEAD_COMMON_VIEW_MENU_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.common.view.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="62" class="link-to-prism">src/modules/shared/constant/permission.const.ts:62</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_GET_ALL_COMPANY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_GET_ALL_COMPANY</b>
                            <a href="#LEAD_GET_ALL_COMPANY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;lead.get.all.company&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="92" class="link-to-prism">src/modules/shared/constant/permission.const.ts:92</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_GET_ALL_SUPER_ADMIN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_GET_ALL_SUPER_ADMIN</b>
                            <a href="#LEAD_GET_ALL_SUPER_ADMIN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;lead.get.all.super.admin&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="91" class="link-to-prism">src/modules/shared/constant/permission.const.ts:91</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_GET_ALL_UNIT"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_GET_ALL_UNIT</b>
                            <a href="#LEAD_GET_ALL_UNIT"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;lead.get.all.unit&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="93" class="link-to-prism">src/modules/shared/constant/permission.const.ts:93</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_MANUAL_DELIVER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_MANUAL_DELIVER</b>
                            <a href="#LEAD_MANUAL_DELIVER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.manual.deliver&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="46" class="link-to-prism">src/modules/shared/constant/permission.const.ts:46</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_PRIMARY_VIEW_MENU"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_PRIMARY_VIEW_MENU</b>
                            <a href="#LEAD_PRIMARY_VIEW_MENU"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.primary.view.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/modules/shared/constant/permission.const.ts:39</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_PRIMARY_VIEW_MENU_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_PRIMARY_VIEW_MENU_CARE</b>
                            <a href="#LEAD_PRIMARY_VIEW_MENU_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.primary.view.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="60" class="link-to-prism">src/modules/shared/constant/permission.const.ts:60</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_BY_EMP"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_BY_EMP</b>
                            <a href="#LEAD_REPORT_BY_EMP"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.report.by.emp&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="43" class="link-to-prism">src/modules/shared/constant/permission.const.ts:43</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_BY_EMP_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_BY_EMP_CARE</b>
                            <a href="#LEAD_REPORT_BY_EMP_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.report.by.emp&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="64" class="link-to-prism">src/modules/shared/constant/permission.const.ts:64</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_BY_IMPORTER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_BY_IMPORTER</b>
                            <a href="#LEAD_REPORT_BY_IMPORTER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.report.by.importer&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="42" class="link-to-prism">src/modules/shared/constant/permission.const.ts:42</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_BY_IMPORTER_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_BY_IMPORTER_CARE</b>
                            <a href="#LEAD_REPORT_BY_IMPORTER_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.report.by.importer&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="63" class="link-to-prism">src/modules/shared/constant/permission.const.ts:63</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_BY_PROJECT_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_BY_PROJECT_CARE</b>
                            <a href="#LEAD_REPORT_BY_PROJECT_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.report.by.project&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="65" class="link-to-prism">src/modules/shared/constant/permission.const.ts:65</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_QUERY_ALL_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_QUERY_ALL_CARE</b>
                            <a href="#LEAD_REPORT_QUERY_ALL_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.report.query.all&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/modules/shared/constant/permission.const.ts:67</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_VIEW_ALL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_VIEW_ALL</b>
                            <a href="#LEAD_REPORT_VIEW_ALL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.report.view.all&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="44" class="link-to-prism">src/modules/shared/constant/permission.const.ts:44</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_VIEW_ALL_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_VIEW_ALL_CARE</b>
                            <a href="#LEAD_REPORT_VIEW_ALL_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.report.view.all&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="66" class="link-to-prism">src/modules/shared/constant/permission.const.ts:66</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_VIEW_MENU"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_VIEW_MENU</b>
                            <a href="#LEAD_REPORT_VIEW_MENU"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.report.view.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="45" class="link-to-prism">src/modules/shared/constant/permission.const.ts:45</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REPORT_VIEW_MENU_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REPORT_VIEW_MENU_CARE</b>
                            <a href="#LEAD_REPORT_VIEW_MENU_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.report.view.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="68" class="link-to-prism">src/modules/shared/constant/permission.const.ts:68</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_REVOKE_DELIVER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_REVOKE_DELIVER</b>
                            <a href="#LEAD_REVOKE_DELIVER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.revoke.deliver&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="48" class="link-to-prism">src/modules/shared/constant/permission.const.ts:48</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_VIEW_MENU"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_VIEW_MENU</b>
                            <a href="#LEAD_VIEW_MENU"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.view.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="40" class="link-to-prism">src/modules/shared/constant/permission.const.ts:40</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEAD_VIEW_MENU_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEAD_VIEW_MENU_CARE</b>
                            <a href="#LEAD_VIEW_MENU_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.view.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="61" class="link-to-prism">src/modules/shared/constant/permission.const.ts:61</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_CREATE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_CREATE</b>
                            <a href="#LEADJOB_CREATE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;leadjob.create&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="78" class="link-to-prism">src/modules/shared/constant/permission.const.ts:78</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_DELETE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_DELETE</b>
                            <a href="#LEADJOB_DELETE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;leadjob.delete&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="81" class="link-to-prism">src/modules/shared/constant/permission.const.ts:81</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_GET"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_GET</b>
                            <a href="#LEADJOB_GET"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;leadjob.get&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="85" class="link-to-prism">src/modules/shared/constant/permission.const.ts:85</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_GET_ALL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_GET_ALL</b>
                            <a href="#LEADJOB_GET_ALL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;leadjob.get.all&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="82" class="link-to-prism">src/modules/shared/constant/permission.const.ts:82</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_GET_BY_ADMIN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_GET_BY_ADMIN</b>
                            <a href="#LEADJOB_GET_BY_ADMIN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;leadjob.get.by.admin&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="84" class="link-to-prism">src/modules/shared/constant/permission.const.ts:84</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_GET_BY_POS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_GET_BY_POS</b>
                            <a href="#LEADJOB_GET_BY_POS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;leadjob.get.by.pos&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="88" class="link-to-prism">src/modules/shared/constant/permission.const.ts:88</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_GET_ID"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_GET_ID</b>
                            <a href="#LEADJOB_GET_ID"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;leadjob.get.id&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="83" class="link-to-prism">src/modules/shared/constant/permission.const.ts:83</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_PROCESS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_PROCESS</b>
                            <a href="#LEADJOB_PROCESS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;leadjob.process&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="80" class="link-to-prism">src/modules/shared/constant/permission.const.ts:80</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_UPDATE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_UPDATE</b>
                            <a href="#LEADJOB_UPDATE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;leadjob.update&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="79" class="link-to-prism">src/modules/shared/constant/permission.const.ts:79</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_VIEW_MENU"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_VIEW_MENU</b>
                            <a href="#LEADJOB_VIEW_MENU"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;leadjob.view.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="86" class="link-to-prism">src/modules/shared/constant/permission.const.ts:86</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LEADJOB_VIEW_PLAN_MENU"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LEADJOB_VIEW_PLAN_MENU</b>
                            <a href="#LEADJOB_VIEW_PLAN_MENU"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;leadjob.view.plan.menu&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="87" class="link-to-prism">src/modules/shared/constant/permission.const.ts:87</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="MANAGE_REPOSITORY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            MANAGE_REPOSITORY</b>
                            <a href="#MANAGE_REPOSITORY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.repository.manage&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="47" class="link-to-prism">src/modules/shared/constant/permission.const.ts:47</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="MANAGE_REPOSITORY_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            MANAGE_REPOSITORY_CARE</b>
                            <a href="#MANAGE_REPOSITORY_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.repository.manage&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="74" class="link-to-prism">src/modules/shared/constant/permission.const.ts:74</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PENDING_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PENDING_LEAD</b>
                            <a href="#PENDING_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.pending&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="11" class="link-to-prism">src/modules/shared/constant/permission.const.ts:11</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PENDING_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PENDING_LEAD_CARE</b>
                            <a href="#PENDING_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.pending&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="26" class="link-to-prism">src/modules/shared/constant/permission.const.ts:26</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROCESS_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROCESS_LEAD</b>
                            <a href="#PROCESS_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.process&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="9" class="link-to-prism">src/modules/shared/constant/permission.const.ts:9</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROCESS_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROCESS_LEAD_CARE</b>
                            <a href="#PROCESS_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.process&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="24" class="link-to-prism">src/modules/shared/constant/permission.const.ts:24</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PULL_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PULL_LEAD</b>
                            <a href="#PULL_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.pull&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="6" class="link-to-prism">src/modules/shared/constant/permission.const.ts:6</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PULL_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PULL_LEAD_CARE</b>
                            <a href="#PULL_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.pull&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="21" class="link-to-prism">src/modules/shared/constant/permission.const.ts:21</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REJECT_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REJECT_LEAD</b>
                            <a href="#REJECT_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.reject&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="7" class="link-to-prism">src/modules/shared/constant/permission.const.ts:7</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REJECT_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REJECT_LEAD_CARE</b>
                            <a href="#REJECT_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.reject&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="22" class="link-to-prism">src/modules/shared/constant/permission.const.ts:22</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REPORT_EXPLOIT_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REPORT_EXPLOIT_LEAD</b>
                            <a href="#REPORT_EXPLOIT_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.report.exploit&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/modules/shared/constant/permission.const.ts:38</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REPORT_EXPLOIT_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REPORT_EXPLOIT_LEAD_CARE</b>
                            <a href="#REPORT_EXPLOIT_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.report.exploit&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="59" class="link-to-prism">src/modules/shared/constant/permission.const.ts:59</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REPORT_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REPORT_LEAD</b>
                            <a href="#REPORT_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.report&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="37" class="link-to-prism">src/modules/shared/constant/permission.const.ts:37</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REPORT_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REPORT_LEAD_CARE</b>
                            <a href="#REPORT_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.report&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="58" class="link-to-prism">src/modules/shared/constant/permission.const.ts:58</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SERVICE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SERVICE</b>
                            <a href="#SERVICE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
        LEAD: {
            NAME: &#x27;lead&#x27;,
            ROLE: [
                PermissionConst.CREATE_LEAD,
                PermissionConst.UPDATE_LEAD,
                PermissionConst.DELETE_LEAD,
                PermissionConst.PULL_LEAD,
                PermissionConst.REJECT_LEAD,
                PermissionConst.FAIL_LEAD,
                PermissionConst.PROCESS_LEAD,
                PermissionConst.UNPROCESS_LEAD,
                PermissionConst.ASSGIN_LEAD,
                PermissionConst.PENDING_LEAD,
                PermissionConst.LEAD_PRIMARY_VIEW_MENU,
                PermissionConst.LEAD_VIEW_MENU,
                PermissionConst.LEAD_COMMON_VIEW_MENU,
                PermissionConst.LEAD_MANUAL_DELIVER,
                PermissionConst.LEAD_REVOKE_DELIVER,

                PermissionConst.GET_EVENTSTREAM,
                PermissionConst.GET_ALL_LEAD_HISTORY,
                PermissionConst.GET_ALL_LEAD,
                PermissionConst.GET_ALL_LEAD_BY_POS,
                PermissionConst.GET_ID_LEAD,
                PermissionConst.GET_LEAD_PROCESS_BY_EMPLOYEE,
                PermissionConst.IMPORT_LEAD,
                PermissionConst.REPORT_LEAD,
                PermissionConst.LEAD_PRIMARY_VIEW_MENU,
                PermissionConst.LEAD_REPORT_BY_IMPORTER,
                PermissionConst.LEAD_REPORT_BY_EMP,
                PermissionConst.LEAD_REPORT_VIEW_ALL,
                PermissionConst.LEAD_REPORT_VIEW_MENU,
                PermissionConst.GET_ALL_LEAD_ADVISING,
                PermissionConst.MANAGE_REPOSITORY,
                PermissionConst.LEAD_GET_ALL_SUPER_ADMIN,
                PermissionConst.LEAD_GET_ALL_COMPANY,
                PermissionConst.LEAD_GET_ALL_UNIT,
            ]
        },
        LEAD_CARE: {
          NAME: &#x27;leadCare&#x27;,
          ROLE: [
            PermissionConst.CREATE_LEAD_CARE,
            PermissionConst.UPDATE_LEAD_CARE,
            PermissionConst.DELETE_LEAD_CARE,
            PermissionConst.PULL_LEAD_CARE,
            PermissionConst.REJECT_LEAD_CARE,
            PermissionConst.FAIL_LEAD_CARE,
            PermissionConst.PROCESS_LEAD_CARE,
            PermissionConst.UNPROCESS_LEAD_CARE,
            PermissionConst.ASSGIN_LEAD_CARE,
            PermissionConst.PENDING_LEAD_CARE,
            PermissionConst.LEAD_PRIMARY_VIEW_MENU_CARE,
            PermissionConst.LEAD_VIEW_MENU_CARE,
            PermissionConst.LEAD_COMMON_VIEW_MENU_CARE,

            PermissionConst.HANDLE_TRANSFER_REQUEST,

            PermissionConst.GET_EVENTSTREAM_CARE,
            PermissionConst.GET_ALL_LEAD_HISTORY_CARE,
            PermissionConst.GET_ALL_LEAD_CARE,
            PermissionConst.GET_ALL_LEAD_BY_POS_CARE,
            PermissionConst.GET_ID_LEAD_CARE,
            PermissionConst.GET_LEAD_PROCESS_BY_EMPLOYEE_CARE,
            PermissionConst.IMPORT_LEAD_CARE,
            PermissionConst.REPORT_LEAD_CARE,
            PermissionConst.LEAD_PRIMARY_VIEW_MENU_CARE,
            PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE,
            PermissionConst.LEAD_REPORT_BY_EMP_CARE,
            PermissionConst.LEAD_REPORT_BY_PROJECT_CARE,
            PermissionConst.LEAD_REPORT_VIEW_ALL_CARE,
            PermissionConst.LEAD_REPORT_QUERY_ALL_CARE,
            PermissionConst.LEAD_REPORT_VIEW_MENU_CARE,
            PermissionConst.GET_ALL_LEAD_ADVISING_CARE,
            PermissionConst.MANAGE_REPOSITORY_CARE,
            PermissionConst.LEAD_CARE_GET_ALL,
            PermissionConst.LEAD_CARE_GET_BY_ADMIN,
            PermissionConst.LEAD_CARE_BQL_GET_ALL,
            PermissionConst.ADMIN_GET_ALL_TRANSER_HISTORY,
          ]
        },
        LEAD_JOB: {
          NAME: &#x27;leadJob&#x27;,
          ROLE: [
            PermissionConst.LEADJOB_CREATE,
            PermissionConst.LEADJOB_UPDATE,
            PermissionConst.LEADJOB_PROCESS,
            PermissionConst.LEADJOB_DELETE,
            PermissionConst.LEADJOB_GET_ALL,
            PermissionConst.LEADJOB_GET_ID,
            PermissionConst.LEADJOB_GET_BY_ADMIN,
            PermissionConst.LEADJOB_GET,
            PermissionConst.LEADJOB_VIEW_MENU,
            PermissionConst.LEADJOB_VIEW_PLAN_MENU,
            PermissionConst.LEADJOB_GET_BY_POS,
          ]
        }
    }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="95" class="link-to-prism">src/modules/shared/constant/permission.const.ts:95</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="UNPROCESS_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            UNPROCESS_LEAD</b>
                            <a href="#UNPROCESS_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.unprocess&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="10" class="link-to-prism">src/modules/shared/constant/permission.const.ts:10</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="UNPROCESS_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            UNPROCESS_LEAD_CARE</b>
                            <a href="#UNPROCESS_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.unprocess&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="25" class="link-to-prism">src/modules/shared/constant/permission.const.ts:25</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="UPDATE_LEAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            UPDATE_LEAD</b>
                            <a href="#UPDATE_LEAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.update&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="4" class="link-to-prism">src/modules/shared/constant/permission.const.ts:4</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="UPDATE_LEAD_CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            UPDATE_LEAD_CARE</b>
                            <a href="#UPDATE_LEAD_CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;lead.care.update&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="19" class="link-to-prism">src/modules/shared/constant/permission.const.ts:19</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ALL_ROLES"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            ALL_ROLES
                        </b>
                        <a href="#ALL_ROLES"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>ALL_ROLES()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="210"
                            class="link-to-prism">src/modules/shared/constant/permission.const.ts:210</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Object[]</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="PERMISSION_PACKAGES"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            PERMISSION_PACKAGES
                        </b>
                        <a href="#PERMISSION_PACKAGES"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>PERMISSION_PACKAGES()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="196"
                            class="link-to-prism">src/modules/shared/constant/permission.const.ts:196</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Object[]</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">export class PermissionConst {
    // Define prermission const
    static CREATE_LEAD &#x3D; &#x27;lead.create&#x27;;
    static UPDATE_LEAD &#x3D; &#x27;lead.update&#x27;;
    static DELETE_LEAD &#x3D; &#x27;lead.delete&#x27;;
    static PULL_LEAD &#x3D; &#x27;lead.pull&#x27;;
    static REJECT_LEAD &#x3D; &#x27;lead.reject&#x27;;
    static FAIL_LEAD &#x3D; &#x27;lead.fail&#x27;;
    static PROCESS_LEAD &#x3D; &#x27;lead.process&#x27;;
    static UNPROCESS_LEAD &#x3D; &#x27;lead.unprocess&#x27;;
    static PENDING_LEAD &#x3D; &#x27;lead.pending&#x27;;
    static ASSGIN_LEAD &#x3D; &#x27;lead.assign&#x27;;

    //
    static HANDLE_TRANSFER_REQUEST &#x3D; &#x27;handle.transfer.request&#x27;;

    // Define prermission const
    static CREATE_LEAD_CARE &#x3D; &#x27;lead.care.create&#x27;;
    static UPDATE_LEAD_CARE &#x3D; &#x27;lead.care.update&#x27;;
    static DELETE_LEAD_CARE &#x3D; &#x27;lead.care.delete&#x27;;
    static PULL_LEAD_CARE &#x3D; &#x27;lead.care.pull&#x27;;
    static REJECT_LEAD_CARE &#x3D; &#x27;lead.care.reject&#x27;;
    static FAIL_LEAD_CARE &#x3D; &#x27;lead.care.fail&#x27;;
    static PROCESS_LEAD_CARE &#x3D; &#x27;lead.care.process&#x27;;
    static UNPROCESS_LEAD_CARE &#x3D; &#x27;lead.care.unprocess&#x27;;
    static PENDING_LEAD_CARE &#x3D; &#x27;lead.care.pending&#x27;;
    static ASSGIN_LEAD_CARE &#x3D; &#x27;lead.care.assign&#x27;;

    static GET_ALL_LEAD_HISTORY &#x3D; &#x27;lead.get.all.lead.history&#x27;;
    static GET_EVENTSTREAM &#x3D; &#x27;lead.get.lead.eventstream&#x27;;
    static GET_ALL_LEAD &#x3D; &#x27;lead.get.all.lead&#x27;;
    static GET_ALL_LEAD_BY_POS &#x3D;&#x27;lead.get.all.by.pos&#x27;;
    static GET_ID_LEAD &#x3D; &#x27;lead.get.id&#x27;;
    static GET_LEAD_PROCESS_BY_EMPLOYEE &#x3D; &#x27;lead.get.process.by.employee&#x27;;
    static IMPORT_LEAD &#x3D; &#x27;lead.import&#x27;;
    static GET_ALL_LEAD_ADVISING &#x3D; &#x27;lead.get.all.lead.advising&#x27;;
    static REPORT_LEAD &#x3D; &#x27;lead.report&#x27;;
    static REPORT_EXPLOIT_LEAD &#x3D; &#x27;lead.report.exploit&#x27;;
    static LEAD_PRIMARY_VIEW_MENU &#x3D; &#x27;lead.primary.view.menu&#x27;;
    static LEAD_VIEW_MENU &#x3D; &#x27;lead.view.menu&#x27;;
    static LEAD_COMMON_VIEW_MENU &#x3D; &#x27;lead.common.view.menu&#x27;;
    static LEAD_REPORT_BY_IMPORTER &#x3D; &#x27;lead.report.by.importer&#x27;;
    static LEAD_REPORT_BY_EMP &#x3D; &#x27;lead.report.by.emp&#x27;;
    static LEAD_REPORT_VIEW_ALL &#x3D; &#x27;lead.report.view.all&#x27;;
    static LEAD_REPORT_VIEW_MENU &#x3D; &#x27;lead.report.view.menu&#x27;;
    static LEAD_MANUAL_DELIVER &#x3D; &#x27;lead.manual.deliver&#x27;;
    static MANAGE_REPOSITORY &#x3D; &#x27;lead.repository.manage&#x27;;
    static LEAD_REVOKE_DELIVER &#x3D; &#x27;lead.revoke.deliver&#x27;;

    static GET_ALL_LEAD_HISTORY_CARE &#x3D; &#x27;lead.care.get.all.lead.history&#x27;;
    static GET_EVENTSTREAM_CARE &#x3D; &#x27;lead.care.get.lead.eventstream&#x27;;
    static GET_ALL_LEAD_CARE &#x3D; &#x27;lead.care.get.all.lead&#x27;;
    static GET_ALL_LEAD_BY_POS_CARE &#x3D;&#x27;lead.care.get.all.by.pos&#x27;;
    static GET_ID_LEAD_CARE &#x3D; &#x27;lead.care.get.id&#x27;;
    static GET_LEAD_PROCESS_BY_EMPLOYEE_CARE &#x3D; &#x27;lead.care.get.process.by.employee&#x27;;
    static IMPORT_LEAD_CARE &#x3D; &#x27;lead.care.import&#x27;;
    static GET_ALL_LEAD_ADVISING_CARE &#x3D; &#x27;lead.care.get.all.lead.advising&#x27;;
    static REPORT_LEAD_CARE &#x3D; &#x27;lead.care.report&#x27;;
    static REPORT_EXPLOIT_LEAD_CARE &#x3D; &#x27;lead.care.report.exploit&#x27;;
    static LEAD_PRIMARY_VIEW_MENU_CARE &#x3D; &#x27;lead.care.primary.view.menu&#x27;;
    static LEAD_VIEW_MENU_CARE &#x3D; &#x27;lead.care.view.menu&#x27;;
    static LEAD_COMMON_VIEW_MENU_CARE &#x3D; &#x27;lead.care.common.view.menu&#x27;;
    static LEAD_REPORT_BY_IMPORTER_CARE &#x3D; &#x27;lead.care.report.by.importer&#x27;;
    static LEAD_REPORT_BY_EMP_CARE &#x3D; &#x27;lead.care.report.by.emp&#x27;;
    static LEAD_REPORT_BY_PROJECT_CARE &#x3D; &#x27;lead.care.report.by.project&#x27;;
    static LEAD_REPORT_VIEW_ALL_CARE &#x3D; &#x27;lead.care.report.view.all&#x27;;
    static LEAD_REPORT_QUERY_ALL_CARE &#x3D; &#x27;lead.care.report.query.all&#x27;;
    static LEAD_REPORT_VIEW_MENU_CARE &#x3D; &#x27;lead.care.report.view.menu&#x27;;

    static LEAD_CARE_GET_ALL &#x3D; &#x27;lead.care.get.all&#x27;;
    static LEAD_CARE_GET_BY_ADMIN &#x3D; &#x27;lead.care.get.by.admin&#x27;;
    static LEAD_CARE_BQL_GET_ALL &#x3D; &#x27;lead.care.bql.get.all&#x27;;

    static MANAGE_REPOSITORY_CARE &#x3D; &#x27;lead.care.repository.manage&#x27;;

    static ADMIN_GET_ALL_TRANSER_HISTORY &#x3D; &#x27;admin.get.all.transfer.history&#x27;;
    // leadjob
    static LEADJOB_CREATE &#x3D; &quot;leadjob.create&quot;;
    static LEADJOB_UPDATE &#x3D; &quot;leadjob.update&quot;;
    static LEADJOB_PROCESS &#x3D; &quot;leadjob.process&quot;;
    static LEADJOB_DELETE &#x3D; &quot;leadjob.delete&quot;;
    static LEADJOB_GET_ALL &#x3D; &quot;leadjob.get.all&quot;;
    static LEADJOB_GET_ID &#x3D; &quot;leadjob.get.id&quot;;
    static LEADJOB_GET_BY_ADMIN &#x3D; &#x27;leadjob.get.by.admin&#x27;;
    static LEADJOB_GET &#x3D; &quot;leadjob.get&quot;;
    static LEADJOB_VIEW_MENU &#x3D; &#x27;leadjob.view.menu&#x27;;
    static LEADJOB_VIEW_PLAN_MENU &#x3D; &#x27;leadjob.view.plan.menu&#x27;;
    static LEADJOB_GET_BY_POS &#x3D; &quot;leadjob.get.by.pos&quot;;

    // lead 
    static LEAD_GET_ALL_SUPER_ADMIN &#x3D; &quot;lead.get.all.super.admin&quot;; // Lấy tất cả dữ liệu lead
    static LEAD_GET_ALL_COMPANY &#x3D; &quot;lead.get.all.company&quot;; // Lấy tất cả dữ liệu lead theo các đơn vị của công ty
    static LEAD_GET_ALL_UNIT &#x3D; &quot;lead.get.all.unit&quot;; // Lấy tất cả dữ liệu của nhân viên đơn vị sàn, team

    static SERVICE &#x3D; {
        LEAD: {
            NAME: &#x27;lead&#x27;,
            ROLE: [
                PermissionConst.CREATE_LEAD,
                PermissionConst.UPDATE_LEAD,
                PermissionConst.DELETE_LEAD,
                PermissionConst.PULL_LEAD,
                PermissionConst.REJECT_LEAD,
                PermissionConst.FAIL_LEAD,
                PermissionConst.PROCESS_LEAD,
                PermissionConst.UNPROCESS_LEAD,
                PermissionConst.ASSGIN_LEAD,
                PermissionConst.PENDING_LEAD,
                PermissionConst.LEAD_PRIMARY_VIEW_MENU,
                PermissionConst.LEAD_VIEW_MENU,
                PermissionConst.LEAD_COMMON_VIEW_MENU,
                PermissionConst.LEAD_MANUAL_DELIVER,
                PermissionConst.LEAD_REVOKE_DELIVER,

                PermissionConst.GET_EVENTSTREAM,
                PermissionConst.GET_ALL_LEAD_HISTORY,
                PermissionConst.GET_ALL_LEAD,
                PermissionConst.GET_ALL_LEAD_BY_POS,
                PermissionConst.GET_ID_LEAD,
                PermissionConst.GET_LEAD_PROCESS_BY_EMPLOYEE,
                PermissionConst.IMPORT_LEAD,
                PermissionConst.REPORT_LEAD,
                PermissionConst.LEAD_PRIMARY_VIEW_MENU,
                PermissionConst.LEAD_REPORT_BY_IMPORTER,
                PermissionConst.LEAD_REPORT_BY_EMP,
                PermissionConst.LEAD_REPORT_VIEW_ALL,
                PermissionConst.LEAD_REPORT_VIEW_MENU,
                PermissionConst.GET_ALL_LEAD_ADVISING,
                PermissionConst.MANAGE_REPOSITORY,
                PermissionConst.LEAD_GET_ALL_SUPER_ADMIN,
                PermissionConst.LEAD_GET_ALL_COMPANY,
                PermissionConst.LEAD_GET_ALL_UNIT,
            ]
        },
        LEAD_CARE: {
          NAME: &#x27;leadCare&#x27;,
          ROLE: [
            PermissionConst.CREATE_LEAD_CARE,
            PermissionConst.UPDATE_LEAD_CARE,
            PermissionConst.DELETE_LEAD_CARE,
            PermissionConst.PULL_LEAD_CARE,
            PermissionConst.REJECT_LEAD_CARE,
            PermissionConst.FAIL_LEAD_CARE,
            PermissionConst.PROCESS_LEAD_CARE,
            PermissionConst.UNPROCESS_LEAD_CARE,
            PermissionConst.ASSGIN_LEAD_CARE,
            PermissionConst.PENDING_LEAD_CARE,
            PermissionConst.LEAD_PRIMARY_VIEW_MENU_CARE,
            PermissionConst.LEAD_VIEW_MENU_CARE,
            PermissionConst.LEAD_COMMON_VIEW_MENU_CARE,

            PermissionConst.HANDLE_TRANSFER_REQUEST,

            PermissionConst.GET_EVENTSTREAM_CARE,
            PermissionConst.GET_ALL_LEAD_HISTORY_CARE,
            PermissionConst.GET_ALL_LEAD_CARE,
            PermissionConst.GET_ALL_LEAD_BY_POS_CARE,
            PermissionConst.GET_ID_LEAD_CARE,
            PermissionConst.GET_LEAD_PROCESS_BY_EMPLOYEE_CARE,
            PermissionConst.IMPORT_LEAD_CARE,
            PermissionConst.REPORT_LEAD_CARE,
            PermissionConst.LEAD_PRIMARY_VIEW_MENU_CARE,
            PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE,
            PermissionConst.LEAD_REPORT_BY_EMP_CARE,
            PermissionConst.LEAD_REPORT_BY_PROJECT_CARE,
            PermissionConst.LEAD_REPORT_VIEW_ALL_CARE,
            PermissionConst.LEAD_REPORT_QUERY_ALL_CARE,
            PermissionConst.LEAD_REPORT_VIEW_MENU_CARE,
            PermissionConst.GET_ALL_LEAD_ADVISING_CARE,
            PermissionConst.MANAGE_REPOSITORY_CARE,
            PermissionConst.LEAD_CARE_GET_ALL,
            PermissionConst.LEAD_CARE_GET_BY_ADMIN,
            PermissionConst.LEAD_CARE_BQL_GET_ALL,
            PermissionConst.ADMIN_GET_ALL_TRANSER_HISTORY,
          ]
        },
        LEAD_JOB: {
          NAME: &#x27;leadJob&#x27;,
          ROLE: [
            PermissionConst.LEADJOB_CREATE,
            PermissionConst.LEADJOB_UPDATE,
            PermissionConst.LEADJOB_PROCESS,
            PermissionConst.LEADJOB_DELETE,
            PermissionConst.LEADJOB_GET_ALL,
            PermissionConst.LEADJOB_GET_ID,
            PermissionConst.LEADJOB_GET_BY_ADMIN,
            PermissionConst.LEADJOB_GET,
            PermissionConst.LEADJOB_VIEW_MENU,
            PermissionConst.LEADJOB_VIEW_PLAN_MENU,
            PermissionConst.LEADJOB_GET_BY_POS,
          ]
        }
    }

    // Use to get all permission packages
    static PERMISSION_PACKAGES(): Object[] {
        let packs &#x3D; [];
        Object.keys(this.SERVICE)
            .map(key &#x3D;&gt; packs.push(
                {
                    permissions: this.SERVICE[key].ROLE,
                    msxName: this.SERVICE[key].NAME
                }
            ));
        console.log(&#x27;PERMISSION_PACKAGES &#x3D;&gt; &#x27;, packs);
        return packs;
    }

    // Use to get all role
    static ALL_ROLES(): Object[] {
        let permissions &#x3D; [];
        Object.keys(this.SERVICE)
            .map(key &#x3D;&gt; permissions.push(...this.SERVICE[key].ROLE));
        return permissions;
    }
}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'PermissionConst.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
