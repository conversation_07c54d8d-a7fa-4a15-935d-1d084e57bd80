import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { IHistoryImportDocument } from '../interfaces/document.interface';
import _ = require('lodash');
import { CommonConst } from '../../../modules/shared/constant/common.const';

@Injectable()
export class HistoryImportQueryRepository {

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IHistoryImportDocument>
  ) { }

  async create(readmodel): Promise<IHistoryImportDocument> {
    return await this.readModel.create(readmodel)
      .then((response) => {
        console.log('create HistoryImport at query side');
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async deleteMany(): Promise<IHistoryImportDocument> {
    return await this.readModel.deleteMany({})
      .then((response) => {
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async findAll(user, query) {
    const { startCreatedDate, endCreatedDate } = query;

    const page: number = parseInt(query['page']) || 1;
    const pageSize: number = parseInt(query['pageSize']) || 10;
    let match: any = {};
    if (!_.isEmpty(query.keywords)) {
      match = {
        fileName: { $regex: new RegExp(query.keywords), $options: 'i' }
      };
    }
    let sort: any = {
      createdDate: -1
    };
    if (!_.isEmpty(query.sort)) {
      sort = this.transformSort(query.sort) || {
        createdDate: -1
      };
    }

    let startDate = 0;
    let endDate = 0;
    if (startCreatedDate && endCreatedDate) {
      startDate = Number(new Date(startCreatedDate));
      endDate = Number(new Date(endCreatedDate)) + 86399999;
      match.createdDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    } else if (startCreatedDate) {
      startDate = Number(new Date(startCreatedDate));
      match.createdDate = { $gte: new Date(startDate) };
    } else if (endCreatedDate) {
      endDate = Number(new Date(endCreatedDate)) + 86399999;
      match.createdDate = { $lte: new Date(endDate) };
    }

    if (user && user.id) {
      match.$or = [
        { 'processBy.id': user.id },
        { processBy: user.id }
      ];
    } else {
      return {
        rows: [],
        totalCount: []
      };
    }

  // vdl
  user?.dataPermission
      .filter(item => item.dataType === 'INTERNAL_ORGCHART')
      .forEach(item => {
          match.$or.push({
              $expr: {
                  $eq: [
                      { $toLower: { $substr: ["$processBy.orgCode", 0, 4] } },
                      item.companyCode.substring(0, 4).toLowerCase()
                  ]
              }
          });
      });

    console.log('match', match);

    return await this.readModel.aggregate([
      {
        $match: match
      },
      {
        $sort: sort
      },
      {
        $facet: {
          rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
          totalCount: [
            {
              $count: 'count'
            }
          ]
        }
      }
    ]).then(result => {
      const total = result[0].totalCount[0] ? result[0].totalCount[0].count : 0;
      return {
        rows: result[0].rows,
        page,
        pageSize,
        total: total,
        totalPages: Math.floor((total + pageSize - 1) / pageSize)
      };
    }).catch((error) => {
      return error;
    });
  }
  protected transformSort(paramSort?: String) {
    let sort: any = paramSort;
    if (_.isString(sort)) {
      sort = sort.split(',');
    }
    if (Array.isArray(sort)) {
      let sortObj = {};
      sort.forEach(s => {
        if (s.startsWith('-'))
          sortObj[s.slice(1)] = -1;
        else
          sortObj[s] = 1;
      });
      return sortObj;
    }

    return sort;
  }
}
