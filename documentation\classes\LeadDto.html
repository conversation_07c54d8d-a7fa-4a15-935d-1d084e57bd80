<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>LeadDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/lead.domain/dto/lead.dto.ts</code>
        </p>


            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                        <code><a href="../classes/ClassBased.html" target="_self" >ClassBased</a></code>
            </p>

            <p class="comment">
                <h3>Implements</h3>
            </p>
            <p class="comment">
                            <code><a href="../interfaces/ILead.html" target="_self" >ILead</a></code>
            </p>


            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#address">address</a>
                            </li>
                            <li>
                                <a href="#advisingType">advisingType</a>
                            </li>
                            <li>
                                <a href="#answerTime">answerTime</a>
                            </li>
                            <li>
                                <a href="#assignDuration">assignDuration</a>
                            </li>
                            <li>
                                <a href="#assignedDate">assignedDate</a>
                            </li>
                            <li>
                                <a href="#callHistory">callHistory</a>
                            </li>
                            <li>
                                <a href="#callId">callId</a>
                            </li>
                            <li>
                                <a href="#code">code</a>
                            </li>
                            <li>
                                <a href="#countAssign">countAssign</a>
                            </li>
                            <li>
                                <a href="#createdDate">createdDate</a>
                            </li>
                            <li>
                                <a href="#customer">customer</a>
                            </li>
                            <li>
                                <a href="#customerId">customerId</a>
                            </li>
                            <li>
                                <a href="#description">description</a>
                            </li>
                            <li>
                                <a href="#direction">direction</a>
                            </li>
                            <li>
                                <a href="#email">email</a>
                            </li>
                            <li>
                                <a href="#endCall">endCall</a>
                            </li>
                            <li>
                                <a href="#expireTime">expireTime</a>
                            </li>
                            <li>
                                <a href="#exploitHistory">exploitHistory</a>
                            </li>
                            <li>
                                <a href="#exploitStatus">exploitStatus</a>
                            </li>
                            <li>
                                <a href="#exploitStatusModifiedBy">exploitStatusModifiedBy</a>
                            </li>
                            <li>
                                <a href="#hasSendSmsAfterSurvey">hasSendSmsAfterSurvey</a>
                            </li>
                            <li>
                                <a href="#id">id</a>
                            </li>
                            <li>
                                <a href="#importedBy">importedBy</a>
                            </li>
                            <li>
                                <a href="#interestedProduct">interestedProduct</a>
                            </li>
                            <li>
                                <a href="#isAppointment">isAppointment</a>
                            </li>
                            <li>
                                <a href="#isCalled">isCalled</a>
                            </li>
                            <li>
                                <a href="#isHot">isHot</a>
                            </li>
                            <li>
                                <a href="#isInNeed">isInNeed</a>
                            </li>
                            <li>
                                <a href="#isVisited">isVisited</a>
                            </li>
                            <li>
                                <a href="#latestAssignHistory">latestAssignHistory</a>
                            </li>
                            <li>
                                <a href="#lifeCycleStatus">lifeCycleStatus</a>
                            </li>
                            <li>
                                <a href="#name">name</a>
                            </li>
                            <li>
                                <a href="#needLoan">needLoan</a>
                            </li>
                            <li>
                                <a href="#note">note</a>
                            </li>
                            <li>
                                <a href="#notes">notes</a>
                            </li>
                            <li>
                                <a href="#otherReason">otherReason</a>
                            </li>
                            <li>
                                <a href="#phone">phone</a>
                            </li>
                            <li>
                                <a href="#pos">pos</a>
                            </li>
                            <li>
                                <a href="#processBy">processBy</a>
                            </li>
                            <li>
                                <a href="#processedDate">processedDate</a>
                            </li>
                            <li>
                                <a href="#processedHistory">processedHistory</a>
                            </li>
                            <li>
                                <a href="#profileUrl">profileUrl</a>
                            </li>
                            <li>
                                <a href="#project">project</a>
                            </li>
                            <li>
                                <a href="#property">property</a>
                            </li>
                            <li>
                                <a href="#reasonNoNeed">reasonNoNeed</a>
                            </li>
                            <li>
                                <a href="#repoConfigCode">repoConfigCode</a>
                            </li>
                            <li>
                                <a href="#repoId">repoId</a>
                            </li>
                            <li>
                                <a href="#source">source</a>
                            </li>
                            <li>
                                <a href="#startCall">startCall</a>
                            </li>
                            <li>
                                <a href="#status">status</a>
                            </li>
                            <li>
                                <a href="#t0">t0</a>
                            </li>
                            <li>
                                <a href="#t1">t1</a>
                            </li>
                            <li>
                                <a href="#t2">t2</a>
                            </li>
                            <li>
                                <a href="#t3">t3</a>
                            </li>
                            <li>
                                <a href="#takeCare">takeCare</a>
                            </li>
                            <li>
                                <a href="#timeOut">timeOut</a>
                            </li>
                            <li>
                                <a href="#timestamp">timestamp</a>
                            </li>
                            <li>
                                <a href="#timezoneclient">timezoneclient</a>
                            </li>
                            <li>
                                <a href="#type">type</a>
                            </li>
                            <li>
                                <a href="#updatedDate">updatedDate</a>
                            </li>
                            <li>
                                <a href="#updatedEmail">updatedEmail</a>
                            </li>
                            <li>
                                <a href="#updatedName">updatedName</a>
                            </li>
                            <li>
                                <a href="#updatedPhone">updatedPhone</a>
                            </li>
                            <li>
                                <a href="#updatedProfileUrl">updatedProfileUrl</a>
                            </li>
                            <li>
                                <a href="#visiblePhone">visiblePhone</a>
                            </li>
                            <li>
                                <a href="#active">active</a>
                            </li>
                            <li>
                                <a href="#modifiedBy">modifiedBy</a>
                            </li>
                            <li>
                                <a href="#softDelete">softDelete</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="address"></a>
                        <span class="name">
                            <b>
                            address</b>
                            <a href="#address"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="42" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:42</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="advisingType"></a>
                        <span class="name">
                            <b>
                            advisingType</b>
                            <a href="#advisingType"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="89" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:89</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="answerTime"></a>
                        <span class="name">
                            <b>
                            answerTime</b>
                            <a href="#answerTime"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="116" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:116</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="assignDuration"></a>
                        <span class="name">
                            <b>
                            assignDuration</b>
                            <a href="#assignDuration"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="128" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:128</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="assignedDate"></a>
                        <span class="name">
                            <b>
                            assignedDate</b>
                            <a href="#assignedDate"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="90" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:90</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="callHistory"></a>
                        <span class="name">
                            <b>
                            callHistory</b>
                            <a href="#callHistory"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>any[]</code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="111" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:111</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="callId"></a>
                        <span class="name">
                            <b>
                            callId</b>
                            <a href="#callId"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="113" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:113</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="code"></a>
                        <span class="name">
                            <b>
                            code</b>
                            <a href="#code"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="85" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:85</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="countAssign"></a>
                        <span class="name">
                            <b>
                            countAssign</b>
                            <a href="#countAssign"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="133" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:133</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="createdDate"></a>
                        <span class="name">
                            <b>
                            createdDate</b>
                            <a href="#createdDate"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="56" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:56</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="customer"></a>
                        <span class="name">
                            <b>
                            customer</b>
                            <a href="#customer"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="79" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:79</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="customerId"></a>
                        <span class="name">
                            <b>
                            customerId</b>
                            <a href="#customerId"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:36</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="description"></a>
                        <span class="name">
                            <b>
                            description</b>
                            <a href="#description"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="54" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:54</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="direction"></a>
                        <span class="name">
                            <b>
                            direction</b>
                            <a href="#direction"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>Object[]</code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="102" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:102</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="email"></a>
                        <span class="name">
                            <b>
                            email</b>
                            <a href="#email"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="70" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:70</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="endCall"></a>
                        <span class="name">
                            <b>
                            endCall</b>
                            <a href="#endCall"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="115" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:115</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="expireTime"></a>
                        <span class="name">
                            <b>
                            expireTime</b>
                            <a href="#expireTime"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="132" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:132</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="exploitHistory"></a>
                        <span class="name">
                            <b>
                            exploitHistory</b>
                            <a href="#exploitHistory"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>IExploitHistory[]</code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="122" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:122</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="exploitStatus"></a>
                        <span class="name">
                            <b>
                            exploitStatus</b>
                            <a href="#exploitStatus"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../miscellaneous/enumerations.html#ExploitEnum" target="_self" >ExploitEnum</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="121" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:121</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="exploitStatusModifiedBy"></a>
                        <span class="name">
                            <b>
                            exploitStatusModifiedBy</b>
                            <a href="#exploitStatusModifiedBy"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="124" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:124</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="hasSendSmsAfterSurvey"></a>
                        <span class="name">
                            <b>
                            hasSendSmsAfterSurvey</b>
                            <a href="#hasSendSmsAfterSurvey"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="109" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:109</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="id"></a>
                        <span class="name">
                            <b>
                            id</b>
                            <a href="#id"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:38</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="importedBy"></a>
                        <span class="name">
                            <b>
                            importedBy</b>
                            <a href="#importedBy"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" target="_blank" >Object</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="118" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:118</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="interestedProduct"></a>
                        <span class="name">
                            <b>
                            interestedProduct</b>
                            <a href="#interestedProduct"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>Object[]</code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="101" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:101</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="isAppointment"></a>
                        <span class="name">
                            <b>
                            isAppointment</b>
                            <a href="#isAppointment"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="104" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:104</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="isCalled"></a>
                        <span class="name">
                            <b>
                            isCalled</b>
                            <a href="#isCalled"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="87" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:87</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="isHot"></a>
                        <span class="name">
                            <b>
                            isHot</b>
                            <a href="#isHot"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="127" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:127</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="isInNeed"></a>
                        <span class="name">
                            <b>
                            isInNeed</b>
                            <a href="#isInNeed"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="98" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:98</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="isVisited"></a>
                        <span class="name">
                            <b>
                            isVisited</b>
                            <a href="#isVisited"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="105" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:105</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="latestAssignHistory"></a>
                        <span class="name">
                            <b>
                            latestAssignHistory</b>
                            <a href="#latestAssignHistory"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../interfaces/IExploitHistory.html" target="_self" >IExploitHistory</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="123" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:123</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="lifeCycleStatus"></a>
                        <span class="name">
                            <b>
                            lifeCycleStatus</b>
                            <a href="#lifeCycleStatus"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="74" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:74</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="name"></a>
                        <span class="name">
                            <b>
                            name</b>
                            <a href="#name"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="40" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:40</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="needLoan"></a>
                        <span class="name">
                            <b>
                            needLoan</b>
                            <a href="#needLoan"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="103" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:103</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="note"></a>
                        <span class="name">
                            <b>
                            note</b>
                            <a href="#note"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="106" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:106</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="notes"></a>
                        <span class="name">
                            <b>
                            notes</b>
                            <a href="#notes"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>Object[]</code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="76" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:76</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="otherReason"></a>
                        <span class="name">
                            <b>
                            otherReason</b>
                            <a href="#otherReason"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="100" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:100</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="phone"></a>
                        <span class="name">
                            <b>
                            phone</b>
                            <a href="#phone"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="44" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:44</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="pos"></a>
                        <span class="name">
                            <b>
                            pos</b>
                            <a href="#pos"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" target="_blank" >Object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="46" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:46</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="processBy"></a>
                        <span class="name">
                            <b>
                            processBy</b>
                            <a href="#processBy"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="50" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:50</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="processedDate"></a>
                        <span class="name">
                            <b>
                            processedDate</b>
                            <a href="#processedDate"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="81" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:81</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="processedHistory"></a>
                        <span class="name">
                            <b>
                            processedHistory</b>
                            <a href="#processedHistory"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>any[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="83" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:83</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="profileUrl"></a>
                        <span class="name">
                            <b>
                            profileUrl</b>
                            <a href="#profileUrl"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="72" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:72</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="project"></a>
                        <span class="name">
                            <b>
                            project</b>
                            <a href="#project"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../interfaces/IProject.html" target="_self" >IProject</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="131" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:131</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="property"></a>
                        <span class="name">
                            <b>
                            property</b>
                            <a href="#property"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="80" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:80</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="reasonNoNeed"></a>
                        <span class="name">
                            <b>
                            reasonNoNeed</b>
                            <a href="#reasonNoNeed"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="99" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:99</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="repoConfigCode"></a>
                        <span class="name">
                            <b>
                            repoConfigCode</b>
                            <a href="#repoConfigCode"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="126" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:126</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="repoId"></a>
                        <span class="name">
                            <b>
                            repoId</b>
                            <a href="#repoId"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="125" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:125</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="source"></a>
                        <span class="name">
                            <b>
                            source</b>
                            <a href="#source"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="78" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:78</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="startCall"></a>
                        <span class="name">
                            <b>
                            startCall</b>
                            <a href="#startCall"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="114" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:114</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="status"></a>
                        <span class="name">
                            <b>
                            status</b>
                            <a href="#status"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="48" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:48</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="t0"></a>
                        <span class="name">
                            <b>
                            t0</b>
                            <a href="#t0"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="60" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:60</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="t1"></a>
                        <span class="name">
                            <b>
                            t1</b>
                            <a href="#t1"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="62" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:62</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="t2"></a>
                        <span class="name">
                            <b>
                            t2</b>
                            <a href="#t2"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="64" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:64</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="t3"></a>
                        <span class="name">
                            <b>
                            t3</b>
                            <a href="#t3"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="66" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:66</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="takeCare"></a>
                        <span class="name">
                            <b>
                            takeCare</b>
                            <a href="#takeCare"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../interfaces/ITakeCare.html" target="_self" >ITakeCare</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="130" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:130</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="timeOut"></a>
                        <span class="name">
                            <b>
                            timeOut</b>
                            <a href="#timeOut"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="68" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:68</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="timestamp"></a>
                        <span class="name">
                            <b>
                            timestamp</b>
                            <a href="#timestamp"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="77" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:77</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="timezoneclient"></a>
                        <span class="name">
                            <b>
                            timezoneclient</b>
                            <a href="#timezoneclient"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="75" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:75</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="type"></a>
                        <span class="name">
                            <b>
                            type</b>
                            <a href="#type"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="52" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:52</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="updatedDate"></a>
                        <span class="name">
                            <b>
                            updatedDate</b>
                            <a href="#updatedDate"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="58" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:58</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="updatedEmail"></a>
                        <span class="name">
                            <b>
                            updatedEmail</b>
                            <a href="#updatedEmail"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="95" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:95</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="updatedName"></a>
                        <span class="name">
                            <b>
                            updatedName</b>
                            <a href="#updatedName"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="93" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:93</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="updatedPhone"></a>
                        <span class="name">
                            <b>
                            updatedPhone</b>
                            <a href="#updatedPhone"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="94" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:94</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="updatedProfileUrl"></a>
                        <span class="name">
                            <b>
                            updatedProfileUrl</b>
                            <a href="#updatedProfileUrl"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="96" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:96</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="visiblePhone"></a>
                        <span class="name">
                            <b>
                            visiblePhone</b>
                            <a href="#visiblePhone"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="129" class="link-to-prism">src/modules/lead.domain/dto/lead.dto.ts:129</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="active"></a>
                        <span class="name">
                            <b>
                            active</b>
                            <a href="#active"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/ClassBased.html" target="_self" >ClassBased</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/ClassBased.html#source" target="_self" >ClassBased:3</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="modifiedBy"></a>
                        <span class="name">
                            <b>
                            modifiedBy</b>
                            <a href="#modifiedBy"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/ClassBased.html" target="_self" >ClassBased</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/ClassBased.html#source" target="_self" >ClassBased:5</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="softDelete"></a>
                        <span class="name">
                            <b>
                            softDelete</b>
                            <a href="#softDelete"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/ClassBased.html" target="_self" >ClassBased</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/ClassBased.html#source" target="_self" >ClassBased:4</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { IsString, Validate, IsEnum, IsNotEmpty } from &#x27;class-validator&#x27;;
import { ClassBased } from &#x27;../../shared/classes/class-based&#x27;;
import {
    ILead,
    IExploitHistory,
    ITakeCare,
    IProject,
} from &#x27;../../shared/services/lead/interfaces/lead.interface&#x27;;
import {
    IsStringNotBlank,
    IsLegalLeadStatus,
    IsLegalSex,
    IsLegalMaritalStatus,
    IsLegalIdentify,
    IsLegalSurvey,
} from &#x27;../../shared/classes/class-validation&#x27;;
import { ApiModelPropertyOptional, ApiModelProperty } from &#x27;@nestjs/swagger&#x27;;
import { Type } from &#x27;class-transformer&#x27;;
import { ExploitEnum } from &#x27;../../shared/enum/exploit.enum&#x27;;

export class ExploitHistory implements IExploitHistory {
    @ApiModelPropertyOptional()
    status: ExploitEnum;

    @ApiModelPropertyOptional()
    updatedAt: Date;

    @ApiModelPropertyOptional()
    takeCareId?: string;

    @ApiModelPropertyOptional()
    updatedBy?: string;
}

export class LeadDto extends ClassBased implements ILead {
    customerId: string;
    @ApiModelPropertyOptional()
    id: string;
    @ApiModelPropertyOptional()
    name: string;
    @ApiModelPropertyOptional()
    address: string;
    @ApiModelPropertyOptional()
    phone: string;
    @ApiModelPropertyOptional()
    pos: Object;
    @ApiModelPropertyOptional()
    status: string;
    @ApiModelPropertyOptional()
    processBy: string;
    @ApiModelPropertyOptional()
    type: string;
    @ApiModelPropertyOptional()
    description: string;
    @ApiModelPropertyOptional()
    createdDate: Date;
    @ApiModelPropertyOptional()
    updatedDate: Date;
    @ApiModelPropertyOptional()
    t0: Date;
    @ApiModelPropertyOptional()
    t1: Date;
    @ApiModelPropertyOptional()
    t2: Date;
    @ApiModelPropertyOptional()
    t3: Date;
    @ApiModelPropertyOptional()
    timeOut: Date;
    @ApiModelPropertyOptional()
    email: string;
    @ApiModelPropertyOptional()
    profileUrl: string;
    @ApiModelPropertyOptional()
    lifeCycleStatus: string;
    timezoneclient: string;
    notes: Object[];
    timestamp: number;
    source: string;
    customer: object;
    property: object;
    processedDate: Date;
    @ApiModelPropertyOptional()
    processedHistory: any[];
    @ApiModelPropertyOptional()
    code: string;
    @ApiModelPropertyOptional()
    isCalled: boolean;
    @ApiModelPropertyOptional()
    advisingType: string;
    assignedDate: Date;

    // new
    updatedName: string;
    updatedPhone: string;
    updatedEmail: string;
    updatedProfileUrl: string;

    isInNeed: string;
    reasonNoNeed: string;
    otherReason: string;
    interestedProduct: Object[];
    direction: Object[];
    needLoan: boolean;
    isAppointment: boolean;
    isVisited: boolean;
    note: string;

    @ApiModelPropertyOptional()
    hasSendSmsAfterSurvey: boolean;

    callHistory: any[];

    callId: string;
    startCall: Date;
    endCall: Date;
    answerTime: number;

    importedBy: Object;

    // Lead repository feature &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;
    exploitStatus: ExploitEnum;
    exploitHistory: IExploitHistory[];
    latestAssignHistory: IExploitHistory;
    exploitStatusModifiedBy: string;
    repoId: string;
    repoConfigCode: string;
    isHot: boolean;
    assignDuration: number;
    visiblePhone: boolean;
    takeCare: ITakeCare;
    project: IProject;
    expireTime: Date;
    countAssign: number;
}

export class CreateLeadDto extends LeadDto {
    @IsString()
    @IsNotEmpty()
    type: string;

    @IsString()
    // @IsNotEmpty()
    address: string;

    // @IsNotEmpty()
    pos: Object;

    recaptcha: string;

    name: string;

    customerId: string;

    timestamp: number;

    source: string;

    customer: object;
    employee: object;
    property: object;
    processedDate: Date;
    code: string; // auto generate code.
    images: Object;
    usedFloorArea: number;
    skipCallHistory: boolean;
    notiUser?: any;
}

export class CreateLeadAdvisingDto extends LeadDto {
    @IsString()
    @IsNotEmpty()
    type: string;
    address: string;
    pos: Object;
    recaptcha: string;
    name: string;
    customerId: string;
    timestamp: number;
    source: string;
    customer: object;
    employee: object;
    property: object;
    processedDate: Date;
    code: string; //auto generate code.
    images: Object;
    advisingType: string;
    usedFloorArea: number;
    skipCallHistory: boolean;
}
export class ImportLeadDemandDto {
    @IsString()
    @IsNotEmpty({message: &#x27;Vui lòng chọn công ty&#x27;})
    companyId: string;
    @IsString()
    // @IsNotEmpty({message: &#x27;Vui lòng chọn sàn giao dịch&#x27;})
    exchangeId: string;
    // @IsString()
    posId: string;
    @IsNotEmpty()
    timestamp: number;
    @IsString()
    @IsNotEmpty({message: &#x27;Vui lòng chọn nguồn&#x27;})
    resource: string;
}

export class AssignLeadDto {

    @ApiModelProperty()
    @IsString()
    @Validate(IsStringNotBlank)
    id: string;

    @ApiModelProperty()
    @IsString()
    @Validate(IsStringNotBlank)
    assignFor: string;
}

export class RequestDto {
    id: string;
    reason: string;
    notes: Object[];
}
export class CallHistoryDto {
    id: string;
    callId: string;
    startCall: Date;
    endCall: Date;
    answerTime: number;
}

export class RejectDto {
    id: string;
    causeReject: string[];
}

export class UpdateStatusDto{
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    @IsString()
    id: string;

    @ApiModelPropertyOptional()
    @IsNotEmpty()
    @IsString()
    @Validate(IsLegalLeadStatus)
    status: string;

    @ApiModelPropertyOptional()
    assignDuration: number;
}
class SurveyObj{
    @ApiModelProperty ()
    type: string;
    @ApiModelProperty ()
    code: string;
    @ApiModelProperty ()
    value: string;
    @ApiModelProperty ()
    name: string;
  }
class IdentifyObj{
    @ApiModelProperty ()
    type: string;
    @ApiModelProperty ()
    num: string;
    @ApiModelProperty ()
    date: string;
    @ApiModelProperty ()
    issueBy: string;
  }
class AddrObj{
    @ApiModelProperty ()
    nation: string;
    @ApiModelProperty ()
    province: string;
    @ApiModelProperty ()
    district: string;
    @ApiModelProperty ()
    ward: string;
  }
export class UpdateLeadDto{
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    @IsString()
    id: string;

    @ApiModelPropertyOptional()
    @Validate(IsLegalSex)
    sex: string;

    @ApiModelPropertyOptional()
    dob: string;
    @ApiModelPropertyOptional()
    subPhone: string[];
    @ApiModelPropertyOptional()
    email: string;
    @ApiModelPropertyOptional({type: AddrObj})
    objAddress: Object;
    @ApiModelPropertyOptional()
    major: string;
    @ApiModelPropertyOptional()
    income: string;
    @ApiModelPropertyOptional()
    sourceIncome: string;
    @ApiModelPropertyOptional()
    assignDuration: string;

    @ApiModelPropertyOptional()
    @Validate(IsLegalMaritalStatus)
    maritalStatus: string;

    @ApiModelPropertyOptional({type:  [SurveyObj]})
    @Validate(IsLegalSurvey)
    surveys: Object[];

    @ApiModelPropertyOptional({type: IdentifyObj})
    @Validate(IsLegalIdentify)
    identification: Object;
    @ApiModelPropertyOptional({type: Object})
    customer: object;
}

export class ImportLeadFromPublicForm extends LeadDto {
    @IsNotEmpty()
    @ApiModelPropertyOptional()
    phone: string;

    @IsNotEmpty()
    @ApiModelPropertyOptional()
    name: string;

    @IsNotEmpty()
    @ApiModelPropertyOptional()
    repoId: string;

    @ApiModelPropertyOptional()
    repoConfigCode: string;

    @ApiModelPropertyOptional()
    isHot: boolean;

    notiUser?: any;
}

export class ImportLeadAsExcelDto {
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    repoId: string;

    @ApiModelPropertyOptional()
    @IsNotEmpty()
    repoConfigCode: string;

    @ApiModelPropertyOptional()
    source: string;
}

export class DeliverLeadDto {
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    id: string;

    @ApiModelPropertyOptional()
    @IsNotEmpty()
    assignee: string;
}

</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadDto.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
