export class QueueConst {
  static TRANSACTION_QUEUE = 'transaction_queue';
  static PROPERTY_QUEUE = 'property_queue';
  static STS_QUEUE = 'sts_queue';
  static NOTIFIER_QUEUE = 'notifier_queue';
  static LOGGER_QUEUE = 'logger_queue';
  static NOTIFICATION_QUEUE = 'notification_queue';
  static CUSTOMER_QUEUE = 'customer_queue';
  static MAILER_QUEUE = 'mailer_queue';
  static EMPLOYEE_QUEUE = 'employee_queue';
  static LEAD_QUEUE = 'lead_queue_hnh';
  static DEMAND_QUEUE = 'demand_queue';
  static ORGCHART_QUEUE = 'orgchart_queue';
  static COMMUNICATION_QUEUE = 'communication_queue';
  static RATING_QUEUE = 'rating_queue';
  static COMMISSION_QUEUE = 'commission_queue';
  static PRIMARY_CONTRACT_QUEUE = 'primary_contract_queue';
  static UPLOADER_QUEUE = 'uploader_queue';
  static SOCIAL_QUEUE = 'social_queue';
  static CHECKIN_QUEUE = 'checkin_queue';
  static MASTERDATA_QUEUE = 'masterdata_queue';
  static MASTERDATA_PRODUCER_QUEUE = 'masterdata_producer_queue';
  static MASTERDATA_ORCHESTRATOR_QUEUE = 'masterdata_orchestrator_queue';
  static MASTERDATA_ORCHESTRATOR_QUEUE_TAM = 'masterdata_orchestrator_queue_tam';
  static PROJECT_QUEUE = 'project_queue';
  static PRODUCT_QUEUE = 'product_queue';
  static PROPOSAL_QUEUE = 'proposal_queue';
  static PARTNER_GATEWAY_QUEUE = 'partner_gateway_queue';
  static NEWS_QUEUE = "news_queue";
  static FB_DYNLINK_QUEUE = 'fb_dynlink_queue';
  static CARE_QUEUE = 'care_queue';
  static COMMENT_QUEUE = 'comment_queue';
  static MASTER_QUEUE = 'master_queue';
  static REPORT_QUEUE = 'report_queue';
  static UTILITY_QUEUE = 'utility_queue';
  static MARKETING_QUEUE = 'marketing_queue';
}
