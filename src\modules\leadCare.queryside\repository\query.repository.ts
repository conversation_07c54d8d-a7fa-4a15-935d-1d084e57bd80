import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { QueryAggregateModel } from '../models/query-aggregate.model';
import { ILeadCareDocument } from '../interfaces/document.interface';
import _ = require('lodash');
import { LifeCycleStatusEnum } from '../../shared/enum/life-cycle-status.enum';
import { SourceEnum } from '../../shared/enum/source.enum';
import { isNullOrUndefined } from 'util';
import { CommonConst } from '../../../modules/shared/constant/common.const';
import { TransactionTypeEnum } from '../../../modules/shared/enum/transaction-type.enum';
import { EmployeeQueryRepository } from '../../employee/repository/query.repository';
import { PermissionConst } from '../../../modules/shared/constant/permission.const';
import { ExploitCareEnum, ExploitEnum } from '../../shared/enum/exploit.enum';
import { ILeadCare } from '../../shared/services/leadCare/interfaces/leadCare.interface';
import * as Bluebird from 'bluebird';
import * as moment from 'moment';
import { MsxLoggerService } from '../../logger/logger.service';
import { PropertyClient } from '../../mgs-sender/property.client';
import { CmdPatternConst } from '../../shared/constant/cmd-pattern.const';
import { AnyNaptrRecord } from 'dns';
import { LeadRepoCareEnum } from '../../../modules/shared/enum/type.enum';


const clc = require("cli-color");

@Injectable()
export class QueryRepository {
    private readonly context = QueryRepository.name;

    constructor(
        @Inject(CommonConst.QUERY_MODEL_TOKEN)
        private readonly readModel: Model<ILeadCareDocument>,
        private readonly employeeRepository: EmployeeQueryRepository,
        private readonly loggerService: MsxLoggerService,
        private readonly propertyClient: PropertyClient
    ) { }

    async findAll(): Promise<ILeadCareDocument[]> {

        return await this.readModel.find()
            .exec()
            .then(result => {
                return result;
            });
    }

    async findAllCommon(query): Promise<ILeadCareDocument[]> {
        const match: any = {
            source : {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]},
            type: query.type
        };
        if (!_.isEmpty(query.keywords)) {
            match['name'] = { $regex: new RegExp(query.keywords), $options: 'i' };
        }
        return await this.readModel.aggregate([
            {
                $match: match
            },
            {
                $lookup: {
                    from: 'employees',
                    localField: 'processBy',
                    foreignField: 'id',
                    as: 'employeeTakeCare'
                }
            }

        ]).allowDiskUse(true)
            .exec();
    }

    async findAllAdvising(): Promise<ILeadCareDocument[]> {
        return await this.readModel.aggregate([

            {
                $match: {
                    type: CommonConst.TYPE.ADVISING
                }
            },
            {
                $lookup: {
                    from: 'employees',
                    localField: 'processBy',
                    foreignField: 'id',
                    as: 'employeeTakeCare'
                }
            }
        ])
            .allowDiskUse(true)
            .exec();
    }

    async findAllPrimary(user, query: any, isTransfer = false): Promise<any> {
        const match: any = {};
        const matchKeywords: any = {};
        let sort: any = {
            code: 1
        };
        // match.type = CommonConst.TYPE.PRIMARY;
        match.$and = [];
        match.$and.push({type: CommonConst.TYPE.PRIMARY});

        if (!user.roles.includes(PermissionConst.LEAD_REPORT_QUERY_ALL_CARE)) {
            if (user && user.id) {
                if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_PROJECT_CARE)) {
                  const projectBqlMember = await this.propertyClient.sendDataPromise({id:user.id},CmdPatternConst.PROJECT.GET_PROJECT_BY_CUSTOMER_SERVICE)
                  match['project.id'] = { $in: projectBqlMember }

                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
                    const emp = await this.employeeRepository.findOne({id: user.id});
                    const $or = [
                      {processBy: {$nin: [null, '']}}, {processBy: {$in: emp.staffIds}},
                      {'takeCare.id': {$nin: [null, '']}}, {'takeCare.id': {$in: emp.staffIds}}
                    ]
                    match.$and.push({$or});
                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
                    match.$and.push({'importedBy.id': user.id});
                } else {
                    const $or = [
                      {processBy: user.id},
                      {'takeCare.id': user.id}
                    ]
                    match.$and.push({$or});
                }
            } else {
               return [];
            }
        }

        if (query) {
            if (!_.isEmpty(query.query)) {
                matchKeywords.$or = [
                    { 'name': { $regex: query.query, $options: 'i' } },
                    { 'phone': { $regex: query.query, $options: 'i' } },
                    { 'code': { $regex: query.query, $options: 'i' } },
                    { 'updatedName': { $regex: query.query, $options: 'i' } },
                    { 'updatedPhone': { $regex: query.query, $options: 'i' } },
                    { 'note': { $regex: query.query, $options: 'i' } },
                    { 'project.name': { $regex: query.query, $options: 'i' } },
                ];
            }

            // Transfer chỉ lấy đc theo pos.
            if (isTransfer) {
                match['repoType'] = LeadRepoCareEnum.TRANSFER;

                // Quyền get all transfer
                if (!user.roles.includes(PermissionConst.ADMIN_GET_ALL_TRANSER_HISTORY)) {
                    match['repoId'] = {$in : query.repoIds ? query.repoIds : []};
                }
            } else {
                match['repoType'] = {$ne : LeadRepoCareEnum.TRANSFER};
            }

            if (!_.isEmpty(query['exploitStatus'])) {
                let exploitStatus = query['exploitStatus'].split(',')
                match['exploitStatus'] = {$in : exploitStatus};
            }
            if (!_.isEmpty(query['resource'])) {
                match['resource'] = query['resource'];
            }

            if (!_.isEmpty(query.employeeId)) {
                match.$and.push({processBy: query.employeeId});
            } else if (!_.isEmpty(query.posId)) {
                match.$and.push({'pos.id': query.posId});
            } else if (!_.isEmpty(query.exchangeId)) {
                match.$and.push({$or: [
                    {'pos.id': query.exchangeId},
                    {'pos.parentId': query.exchangeId}
                ]});
            }

            if(!_.isEmpty(query['customerId'])) {
                match.$and.push({customerId: query['customerId']})
            }

            if (!_.isEmpty(query.assignedDateFrom)) {
                match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
            }

            if (!_.isEmpty(query.assignedDateTo)) {
                match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
            }

            if (!_.isEmpty(query.sort)) {
                sort = this.transformSort(query.sort) || {
                    code: 1
                };
            }

            if (!_.isEmpty(query['createdFrom']) || !_.isEmpty(query['createdTo'])) {
                match['createdDate'] = {};
                if (!_.isEmpty(query['createdFrom'])) {
                    match['createdDate']['$gte'] = new Date(parseInt(query['createdFrom']));
                }
                if (!_.isEmpty(query['createdTo'])) {
                    match['createdDate']['$lte'] = new Date(parseInt(query['createdTo']));
                }
            }
            if (!_.isEmpty(query.forBQL)) {
                match['forBQL'] = true;
            } else {
                match['forBQL'] = { $ne: true };
            }
        }

        const aggregate: any[] = [
            {
                $match: matchKeywords
            },
            {
                $match: match
            },
            { $sort: sort },
        ]
        console.log(JSON.stringify(aggregate))
        if (!_.isEmpty(query['page']) || !_.isEmpty(query['pageSize'])) {
            const page: number = parseInt(query['page']) || 1;
            const pageSize: number = parseInt(query['pageSize']) || 10;
            aggregate.push({
                $facet: {
                    rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                    totalCount: [
                        {
                            $count: 'count'
                        }
                    ]
                }
            });
        }
        // console.log(JSON.stringify(aggregate));
        return await this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
    async getByCustomer(customerId) {
        return await this.readModel.find({ customerId })
            .sort({createdDate: -1})
            .exec()
            .then(result => {
                return result;
            });
    }

    async getAllByPos(posId: String): Promise<ILeadCareDocument[]> {

        return await this.readModel.aggregate([
            { $match: { 'pos.id': posId } },
            { $sort: { createDate: 1 } }
        ]).allowDiskUse(true)
            .exec()
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return err;
            });
    }

    async findOne(query, fields = {}): Promise<ILeadCareDocument> {

        return await this.readModel.findOne(query, fields)
            .exec()
            .then(result => {
                return result;
            });
    }

    async findTransferByContractId(customerId: String, contractId: String): Promise<ILeadCareDocument[]> {
        return await this.readModel.aggregate([
            { 
                $match: { 
                    "customData.contractId":contractId,
                    "repoType":"transfer",
                    "customerId":customerId
                } 
            },
            { $sort: { createdDate: -1 } },
            { $limit: 1}
        ]).allowDiskUse(true)
            .exec()
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return err;
            });
    }

    async findLeadCareById(id: string): Promise<ILeadCareDocument> {
        return await this.readModel.aggregate(
            [
                {
                    $match: { id }
                },
                {
                    $lookup: {
                        from: 'lead-repo-cares',
                        localField: 'repoId',
                        foreignField: 'id',
                        as: 'configData'
                    }
                }
            ]).allowDiskUse(true)
            .exec()
            .then((response) => {
                if (response.length > 0) {
                    if  (response[0].configData.length){
                        response[0].configData = response[0].configData[0].configs.find((item) => item.code === response[0].repoConfigCode);
                    } else {
                        response[0].configData = null;
                    }
                    return response[0];
                }
                return null;
            })
            .catch((exp) => {
                return exp;
            });
    }

    async findAggregateModelById(id: string): Promise<QueryAggregateModel> {
        return await this.readModel.findOne({ id })
            .exec()
            .then((response) => {
                console.log('findAggregateModelById leadCare query side');
                return new QueryAggregateModel(id);
            })
            .catch((exp) => {
                return exp;
            });
    }

    async create(readModel): Promise<ILeadCareDocument> {
        return await this.readModel.create(readModel)
            .then((response) => {
                console.log('createEvent LeadCare at query side');
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async update(model): Promise<ILeadCareDocument> {
        model.updatedDate = Date.now();
        return await this.readModel.update({ id: model.id }, model)
            .then((response) => {
                console.log('update LeadCare at query side');
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async checkDuplicate(query): Promise<boolean> {

        return await this.readModel.aggregate(
            [
                { $match: { name: query.name } }
            ]).allowDiskUse(true)
            .exec()
            .then((result) => {
                if (result && result.length > 0) {
                    return true;
                }
                return false;
            });
    }

    async checkDuplicateUpdate(query): Promise<boolean> {

        return await this.readModel.aggregate(
            [
                {
                    $match: {
                        $and: [
                            { name: query.name },
                            { id: { $ne: query.id } }
                        ]
                    }
                }
            ]).allowDiskUse(true)
            .exec()
            .then((result) => {
                if (result && result.length > 0) {
                    return true;
                }
                return false;
            })
            .catch((error) => {
                throw error;
            });
    }

    async assignLeadCareForEmployee(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    processBy: model.processBy,
                    timeOut: model.timeOut,
                    serverTime: model.serverTime,
                    utcTime: model.utcTime
                }
            },
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    // Update status
    async updateStatus(model) {
        return await this.readModel.updateMany(
            { id: { $in: model.ids } },
            { $set: { status: model.status } },
            { multi: true }
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async findLeadCaresProcessBy(loggedUser, type) {
        const match: any = {};
        if (type) {
            match.type = type;
        }

         // get employee 
         const employee = await this.employeeRepository.findOne( { "id": loggedUser.id} );
         if (!employee) {
             return [];
         }
        return await this.readModel.aggregate([
            { $match: { match, 
                'leadCare.processBy': {
                    $exists: true, $nin: [null, ""],
                    '$in': employee.staffIds
                    }
                } 
            },
            // {
            //     $lookup: {
            //         from: 'employees',
            //         let: { processBy: '$processBy' },
            //         pipeline: [
            //             {
            //                 $match:
            //                 {
            //                     $expr:
            //                     {
            //                         $and:
            //                             [
            //                                 { $in: ['$$processBy', '$staffIds'] },
            //                                 { $eq: [loggedUser.id, '$id'] },
            //                             ]
            //                     }
            //                 }
            //             },
            //         ],
            //         as: 'data'
            //     }
            // },
            // { $unwind: '$data' },


            // {
            //     $project: {
            //         status: '$$ROOT.status',
            //         description: '$$ROOT.description',
            //         active: '$$ROOT.active',
            //         modifiedBy: '$$ROOT.modifiedBy',
            //         employeeTakeCare: '$$ROOT.employeeTakeCare',
            //         customer: '$$ROOT.customer',
            //         pos: '$$ROOT.pos',
            //         ticketId: '$$ROOT.ticketId',
            //         resource: '$$ROOT.resource',
            //         id: '$$ROOT.id',
            //         createDate: '$$ROOT.createDate',
            //         // employee: '$data' // if you want to get employee
            //     }
            // },
            { $sort: { createDate: 1 } },
            {
                $project: {
                    info: '$$ROOT',
                    isOwner: {
                        $cond: {
                            if: { $eq: [loggedUser.id, '$processBy'] },
                            then: 'owners',
                            else: 'others'
                        }
                    }
                }
            },
            {
                $group:
                {
                    _id: '$isOwner',
                    list: { $push: '$info' }
                }
            }
        ]).allowDiskUse(true)
            .exec()
            .then(rs => {
                const result = {};
                rs.forEach(e => {
                    result[e._id] = e.list;
                });
                return result;
            })
            .catch(err => {
                return err;
            });
    }
    /**
     *
     * @param loggedUser user
     * @param {other || own }processType
     * @param query
     */
    async listLeadCaresProcessBy(loggedUser: any, query: any) {
        const processType = query.processType || 'other';
        const limit = Number(query.pageSize) || 10;
        const skip = (Number(query.page) || 1) * limit - limit;
        const q: any = {
            processBy: processType === 'other' ? { $nin: [loggedUser.id, null] } : loggedUser.id,
            lifeCycleStatus: { $nin: [LifeCycleStatusEnum.REMOVED, LifeCycleStatusEnum.PRIMARY_PROCESSING] },
            timeOut: { "$gt" : Date.now() } 

        };
        if (query.type){
            q.type = query.type;
        } else {
            q.type = { $ne : CommonConst.TYPE.PRIMARY}
        }
        let sort: any = {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sort = this.transformSort(query.sort);
        }
        return await this.readModel.find(q)
            .sort(sort)
            .skip(skip)
            .limit(limit)
            .exec();
    }
    async count(query: any = {}) {
        return this.readModel.countDocuments(query).exec();
    }
    async isValidBeforePull(employeeId, type: string) {
        const match: any = { 
            processBy: employeeId, 
            lifeCycleStatus: { $nin: [LifeCycleStatusEnum.PENDING, LifeCycleStatusEnum.REMOVED, LifeCycleStatusEnum.COMPLETED, LifeCycleStatusEnum.PRIMARY_PROCESSING] },
            timeOut: { "$gt" : Date.now() }
        };
        if (type) {
            match.type = type;
        } else {
            match.type = { $ne: CommonConst.TYPE.PRIMARY}
        }
        return await this.readModel.find(match)
            .exec()
            .then(rs => {
                if (rs && rs.length > 0) return false;
                return true;
            })
            .catch(err => {
                return err;
            });
    }

    async findLeadCaresToAssign(employeeId: string, posId: string, exchangeId: string, type: string, shared: boolean = false) {
        const match: any = {
            processBy: null,
            $or: [{'pos.id': posId},
                    {'pos.id': exchangeId}
                ],
            // 'processedHistory.processBy': { $nin: [employeeId] },
            // 'processedHistory.isReject': { $ne: true },
            lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
        };
        if (shared) {
            match.$or.push({'pos.id': 'dxs-shared'});
        }
        if (type) {
            match.type = type;
        } else {
            match.type = { $ne: CommonConst.TYPE.ADVISING };
        }

        return await this.readModel.aggregate([
            {
                $match: match
            },
            { $sort: { createdDate: 1 } },
            { $limit: 1 }   // Update: chỉ cho phép xử lý yêu cầu mỗi lần lấy
        ]).allowDiskUse(true)
            .exec()
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return err;
            });
    }

    async processingLeadCare(id: String) {
        let lifeCycleStatus = LifeCycleStatusEnum.PROCESSING;
        const model = await this.readModel.findOne({id});
        if (model.type === CommonConst.TYPE.PRIMARY) {
            lifeCycleStatus = LifeCycleStatusEnum.PRIMARY_PROCESSING;
        }
        return await this.readModel.updateOne(
            { id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus
                }
            }
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async unprocessingLeadCare(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus: LifeCycleStatusEnum.ASSIGNED, note: model.note
                }
            }
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async delete(id: string) {
        return await this.readModel.deleteOne({ id })
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }
    async deleteMany() {
        return await this.readModel.deleteMany({})
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async assignLeadCare(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    processBy: model.processBy,
                    timeOut: model.timeOut,
                    lifeCycleStatus: model.lifeCycleStatus,
                    timezoneclient: model.timezoneclient,
                    assignedDate: Date.now()
                }
            },
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }
    async closeTicketLeadCare(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus: model.lifeCycleStatus,
                    timezoneclient: model.timezoneclient,
                    exploitHistory:model.exploitHistory,
                    status:model.status,
                    exploitStatus:model.exploitStatus
                }
            },
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async isReadyToAssign(leadCareId: string) {
        return await this.readModel.find({ id: leadCareId, processBy: null })
            .exec()
            .then(result => {
                if (isNullOrUndefined(result)) return false;

                return true;
            });
    }

    async isValidMarkProcessing(employeeId: string, leadCareId) {
        return await this.readModel.find({ processBy: employeeId, lifeCycleStatus: LifeCycleStatusEnum.PROCESSING, id: leadCareId })
            .exec()
            .then(result => {
                if (isNullOrUndefined(result) || result.length === 0)
                    return true;
                return false;
            });
    }



    // Reassign
    async findLeadCaresAssignedEmployee(employeeId: string) {
        return await this.readModel.find({ processBy: employeeId })
            .exec()
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return err;
            });
    }

    async findLeadCaresAssigned() {
        return await this.readModel.find({ processBy: { $ne: null }, lifeCycleStatus: { $nin: [LifeCycleStatusEnum.REMOVED, LifeCycleStatusEnum.PRIMARY_PROCESSING] } })
            .exec()
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return err;
            });
    }

    async reassignLeadCare(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set:
                {
                    updatedDate: Date.now(),
                    modifiedBy: model.modifiedBy,
                    processBy: model.processBy,
                    timeOut: null,
                    lifeCycleStatus: model.lifeCycleStatus,
                    pos: model.pos,
                    processedHistory: model.processedHistory
                }
            }
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async failLeadCare(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set:
                {
                    updatedDate: Date.now(),
                    modifiedBy: model.modifiedBy,
                    processBy: model.processBy,
                    timeOut: null,
                    lifeCycleStatus: model.lifeCycleStatus,
                    pos: model.pos,
                    processedHistory: model.processedHistory
                }
            }
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }
    async updateOne(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: model
            }
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async updateByQuery(query: any, updateQuery) {
        return await this.readModel.updateOne(
            query,
            updateQuery
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async expiredLeadCare(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set:
                {
                    updatedDate: Date.now(),
                    modifiedBy: model.modifiedBy,
                    processBy: model.processBy,
                    timeOut: null,
                    lifeCycleStatus: model.lifeCycleStatus,
                    processedHistory: model.processedHistory
                }
            }
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    // Pending
    async pendingLeadCare(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus: LifeCycleStatusEnum.PENDING,
                    note: model.note
                }
            },
        )
            .exec()
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async countLifeCycleStatusByEmployee(employeeId: String, lifeCycleStatus: string) {
        return await this.readModel.find(
            {
                processBy: employeeId,
                lifeCycleStatus,
            })
            .countDocuments()
            .then(rs => {
                return rs;
            })
            .catch(ex => {
                return ex;
            });
    }

    async countLifeCycleStatusForLeadCares(leadCareIds, lifeCycleStatus) {
        return await this.readModel.find(
            {
                id: { $in: leadCareIds },
                lifeCycleStatus,
            })
            .countDocuments()
            .then(rs => {
                return rs;
            })
            .catch(ex => {
                return ex;
            });
    }
    async countAllTickets() {
        return this.readModel.countDocuments().exec()
            .then(result => {
                return { 'totalTickets': result };
            });
    }

    async countProcessTicketsDemandByUser(query) {
        return await this.readModel.find(
            {
                $and: [
                    query,
                    {
                        $or: [
                            { type: 'BUY' },
                            { type: 'RENT' },
                        ]
                    }
                ]
            },
        ).countDocuments()
            .exec()
            .then(demand => {
                return demand;
            });
    }
    async countProcessTicketsConsignmentByUser(query) {
        return await this.readModel.find(
            {
                $and: [
                    query,
                    {
                        $or: [
                            { type: 'SELL' },
                            { type: 'LEASE' }
                        ]
                    }
                ]
            },
        ).countDocuments()
            .exec()
            .then(consignment => {
                return consignment;
            });
    }
    async countLeadCaresProcessBy(loggedUser: any, query: any = {}): Promise<number> {
        const processType = query.processType || 'other';
        const q = {
            processBy: processType === 'other' ? { $nin: [loggedUser.id, null] } : loggedUser.id,
            lifeCycleStatus: { $ne: LifeCycleStatusEnum.REMOVED },
            timeOut: { "$gt" : Date.now() } 
        };
        return await this.readModel.countDocuments(q).exec();
    }
    /**
     *
     * @param page
     * @param pageSize
     * @param query
     */
    async listAllCommon(user: any, page: number = 1, pageSize: number = 10, query: any = {}): Promise<ILeadCareDocument[]> {
        let sort: any = {
            createdDate: -1
        };
        const match: any = {};
        const agg = [];
        match.$and = [];

        match.$and.push({lifeCycleStatus: { $ne: LifeCycleStatusEnum.REMOVED }});

        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL_CARE)) {
            if (user && user.id) {
                if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
                    const emp = await this.employeeRepository.findOne({id: user.id});
                    match.$and.push({processBy: {$nin: [null, '']}}, {processBy: {$in: emp.staffIds}});
                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
                    match.$and.push({'importedBy.id': user.id});
                } else {
                    return [];
                }
            } else {
               return [];
            }
        }

        if (!_.isEmpty(query.resource)) {
            match.source = query.resource;
        } else {
            match.source = {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]};
        }

        if (!_.isEmpty(query.type)) {
            match.type = { $in: query.type.split(',') };
        }
        if (!_.isEmpty(query.posId)) {
            match['pos.id'] = query.posId;
        }

        if (!_.isEmpty(query.exchangeId)) {
            match.$and.push({$or: [
                {'pos.id': query.exchangeId},
                {'pos.parentId': query.exchangeId}
            ]});
        }

        if (!_.isEmpty(query.assignedDateFrom)) {
            match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
        }

        if (!_.isEmpty(query.assignedDateTo)) {
            match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
        }

        if (!_.isEmpty(query['createdFrom']) || !_.isEmpty(query['createdTo'])) {
            match['createdDate'] = {};
            if (!_.isEmpty(query['createdFrom'])) {
                match['createdDate']['$gte'] = new Date(parseInt(query['createdFrom']));
            }
            if (!_.isEmpty(query['createdTo'])) {
                match['createdDate']['$lte'] = new Date(parseInt(query['createdTo']));
            }
        }
        if (!_.isEmpty(query.lifeCycleStatus)) {
            match.lifeCycleStatus = { $in: query.lifeCycleStatus.split(',') };
        } else {
            agg.push(
                {
                    $lookup: {
                        from: 'employees',
                        localField: 'processBy',
                        foreignField: 'id',
                        as: 'employeeTakeCare'
                    }
                });
        }
        let matchKeywords: any = {};
        if (!_.isEmpty(query.q)) {
            matchKeywords['$or'] = [
                { $and: [{ 'name': { $regex: query.q, $options: 'i' } }, { $or: [{ type: 'BUY' }, { type: 'RENT' }] }] },
                { 'pos.name': { $regex: query.q, $options: 'i' } },
                { $and: [{ 'property.address': { $regex: query.q, $options: 'i' } }, { $or: [{ type: 'SELL' }, { type: 'LEASE' }] }] },
                { $and: [{ 'category.name': { $regex: query.q, $options: 'i' } }, { $or: [{ type: 'SELL' }, { type: 'LEASE' }] }] },
            ];
        }
        if (!_.isEmpty(query.sort)) {
            sort = this.transformSort(query.sort) || {
                createdDate: -1
            };
        }
        
        agg.push({
            $addFields: {
                callHistoryCount: {
                    $cond: [{ $isArray: '$callHistory' }, {
                        $size: {
                            $filter: {
                                input: "$callHistory",
                                as: "e",
                                cond: { $eq: ["$$e.isCalled", true] }
                            }
                        }
                    }, 0]
                },
                callHistoryMinuteCount: {
                    $cond: [{ $isArray: '$callHistory' }, {
                        $sum: "$callHistory.answerTime"
                    }, 0]
                }
            }
        });
        agg.push({
            $match: match
        },
        {
            $match: matchKeywords
        },
        {
            $sort: sort
        },
        {
            $facet: {
                rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                totalCount: [
                    {
                        $count: 'count'
                    }
                ]
            }
        });

        return await this.readModel.aggregate(agg).allowDiskUse(true)
            .exec();
    }
    /**
     * Lấy danh sách yêu cầu tư vấn dịch vụ
     * @param page
     * @param pageSize
     * @param query
     */
    async listAllAdvising(page: number = 1, pageSize: number = 10, query): Promise<ILeadCareDocument[]> {
        let sort: any = {
            createdDate: - 1
        };
        if (!_.isEmpty(query.sort)) {
            sort = this.transformSort(query.sort) || {
                createdDate: - 1
            };
        }
        return await this.readModel.aggregate([
            {
                $lookup: {
                    from: 'employees',
                    localField: 'processBy',
                    foreignField: 'id',
                    as: 'employeeTakeCare'
                }
            },
            {
                $match: {
                    type: CommonConst.TYPE.ADVISING
                }
            },
            { $sort : sort },
            { $skip: (page * pageSize) - pageSize },
            { $limit: pageSize }
        ])
            .allowDiskUse(true)
            .exec();
    }

    /**
     *
     * @param {Array<String>} customerId
     */
    findLeadCareByCustomerId(customerId: string[] = [], mapping: boolean = false) {
        return this.readModel.find({ customerId: { $in: customerId, $exists: true, $nin: [null, ''] } }).exec()
            .then((res) => {
                if (mapping) {
                    return _.groupBy(res, 'customerId');
                }
                return res;
            });
    }
    protected transformSort(paramSort?: String) {
        let sort: any = paramSort;
        if (_.isString(sort)) {
            sort = sort.split(',');
        }
        if (Array.isArray(sort)) {
            let sortObj = {};
            sort.forEach(s => {
                if (s.startsWith('-'))
                    sortObj[s.slice(1)] = -1;
                else
                    sortObj[s] = 1;
            });
            return sortObj;
        }

        return sort;
    }
    async filter(query: any = {}, isPaging: boolean = false) {
        const limit = parseInt(query['pageSize']) || 10;
        const skip = (parseInt(query['page']) || 1) * limit - limit;
        const { page, pageSize, ...q } = query;
        let sort: any = {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sort = this.transformSort(query.sort);
        }
        const aggregate = [
            {
                $match: q
            },
            {
                $lookup: {
                    from: 'customers',
                    localField: 'customerId',    // field in the orders collection
                    foreignField: 'id',  // field in the items collection
                    as: 'customers'
                }
            },
            {
                $lookup: {
                    from: 'employees',
                    localField: 'processBy',    // field in the orders collection
                    foreignField: 'id',  // field in the items collection
                    as: 'employees'
                }
            },
            {
                $addFields: {
                    customer: {
                        $ifNull: ['$customer', { $arrayElemAt: ['$customers', 0] }]
                    },
                    employeeTakeCare: { $arrayElemAt: ['$employees', 0] }
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0
                }
            }
        ];
        if (isPaging) {
            return await this.readModel.aggregate(aggregate)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .exec();
        } else {
            return await this.readModel.aggregate(aggregate)
                .sort(sort)
                .exec();
        }
    }
    async filterReport(user: any = {}, query: any = {}, posInPool: any[] = [], isPaging: boolean = false) {
        const limit = parseInt(query['pageSize']) || 10;
        const skip = (parseInt(query['page']) || 1) * limit - limit;
        const { page, pageSize, sort = '', ...q } = query;
        let sortObject: any = {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sortObject = this.transformSort(query.sort);
        }
        const aggregate = [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    lifeCycleStatus: { $in: [LifeCycleStatusEnum.ASSIGNED, LifeCycleStatusEnum.PROCESSING], },
                    processBy: { $exists: true, $nin: [null, ''] }
                }
            },
            {
                $lookup: {
                    from: 'customers',
                    let: { customerId: '$customerId' },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: ['$$customerId', '$id'] },
                                        ]
                                }


                            }
                        },
                        {
                            $addFields: {
                                'personalInfo.phone': {
                                    $cond: { if: { $ne: ['$modifiedBy', user.id] }, then: '******', else: '$personalInfo.phone' },
                                },
                                'phone': {
                                    $cond: { if: { $ne: ['$modifiedBy', user.id] }, then: '******', else: '$phone' },
                                },
                            }
                        }
                    ],
                    as: 'customers'
                }
            },
            {
                $lookup: {
                    from: 'employees',
                    let: { employeeId: '$processBy' },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $in: ['$$employeeId', '$staffIds'] },
                                            { $eq: [user.id, '$id'] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: 'employeeTakeCares'
                }
            },
            { $unwind: '$employeeTakeCares' },
            {
                $lookup: {
                    from: 'employees',
                    let: { employeeId: '$processBy' },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: ['$$employeeId', '$id'] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: 'employeeTakeCare'
                }
            },
            { $unwind: '$employeeTakeCare' },
            {
                $addFields: {
                    customer: {
                        $ifNull: ['$customer', { $arrayElemAt: ['$customers', 0] }]
                    },
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0,
                    employeeTakeCaress: 0,
                    employeeTakeCares: 0
                }
            },
            {
                $match: q
            },
        ];
        if (isPaging) {
            return await this.readModel.aggregate(aggregate)
                .sort(sortObject)
                .skip(skip)
                .limit(limit)
                .allowDiskUse(true)
                .exec();
        } else {
            return await this.readModel.aggregate(aggregate)
                .sort(sortObject)
                .allowDiskUse(true)
                .exec();
        }
    }

    countReport(user: any = {}, query: any = {}, posInPool: string[] = [], ) {
        const { page, pageSize, ...q } = query;
        const aggregate = [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    pos: { $exists: true, $nin: [null, ''] },
                    $or: [{
                        lifeCycleStatus: { $in: [LifeCycleStatusEnum.ASSIGNED, LifeCycleStatusEnum.PROCESSING] },
                        processBy: { $exists: true, $nin: [null, ''] }
                    }, {
                        'lifeCycleStatus': LifeCycleStatusEnum.IN_POOL,
                        'pos.id': { $exists: true, $nin: [null, ''], $in: posInPool }
                    }]

                }
            },
            {
                $lookup: {
                    from: 'customers',
                    let: { customerId: '$customerId' },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: ['$$customerId', '$id'] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: 'customers'
                }
            },
            {
                $lookup: {
                    from: 'employees',
                    let: { employeeId: '$processBy', posId: '$pos.id' },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $in: ['$$employeeId', '$staffIds'] },
                                            { $eq: [user.id, '$id'] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: 'employeeTakeCares'
                }
            },
            { $unwind: '$employeeTakeCares' },
            {
                $lookup: {
                    from: 'employees',
                    let: { employeeId: '$processBy' },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: ['$$employeeId', '$id'] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: 'employeeTakeCare'
                }
            },
            { $unwind: '$employeeTakeCare' },
            {
                $addFields: {
                    customer: {
                        $ifNull: ['$customer', { $arrayElemAt: ['$customers', 0] }]
                    },
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0,
                }
            },
            {
                $match: q
            },
        ];
        return this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
    countReportInPool(user: any = {}, query: any = {}, posInPool: string[] = []) {
        const { page, pageSize, ...q } = query;
        const aggregate = [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    pos: { $exists: true, $nin: [null, ''] },
                    $or: [{
                        'lifeCycleStatus': LifeCycleStatusEnum.IN_POOL,
                        'pos.id': { $exists: true, $nin: [null, ''], $in: posInPool }
                    }]

                }
            },
            {
                $lookup: {
                    from: 'customers',
                    let: { customerId: '$customerId' },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: ['$$customerId', '$id'] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: 'customers'
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0,
                }
            },
            {
                $match: q
            },
        ];
        return this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
    async countLeadCareReadyForAssigningByPOS(posId: string, type: string) {
        const match: any = {
            'processBy': null,
            'pos.id': posId,
            'lifeCycleStatus': LifeCycleStatusEnum.IN_POOL,
        };
        if (type) {
            match.type = type;
        } else {
            match.type = { $ne: CommonConst.TYPE.ADVISING };
        }
        return await this.readModel.find(match)
            .countDocuments()
            .then(rs => {
                return rs;
            })
            .catch(ex => {
                return ex;
            });
    }

    async countLeadCareReadyForAssigningByEmp(employee: any, type: string) {
        const match: any = {
            processBy: null,
            $or: [{'pos.id': employee.pos.id},
                    {'pos.id': employee.pos.parentId}
                ],
            lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
        };
        if (employee.pos.taxNumber === '3602545493') { // Hard code DXS Tax No to get leadCare from shared pool
            match.$or.push({'pos.id': 'dxs-shared'});
        }
        if (type) {
            match.type = type;
        } else {
            match.type = { $ne: CommonConst.TYPE.ADVISING };
        }
        return await this.readModel.find(match)
            .countDocuments()
            .then(rs => {
                return rs;
            })
            .catch(ex => {
                return ex;
            });
    }

    findLeadCareByEmployeeId(employeeId: string[] = []) {
        return this.readModel.find({
            processBy: { $in: employeeId, $exists: true, $nin: [null, ''] }, type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
        }).exec();
    }

    async countPrimaryLeadCare(user: any, query: any) {
        const page: number = parseInt(query['page']) || 1;
        const pageSize: number = parseInt(query['pageSize']) || 10;
        const aggregate: any = [];
        const match: any = {};

        match.$and = [];
        match.$and.push({ type: CommonConst.TYPE.PRIMARY });

        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL_CARE)) {
            if (user && user.id) {
                if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
                    const emp = await this.employeeRepository.findOne({id: user.id});
                    match.$and.push({processBy: {$nin: [null, '']}}, {processBy: {$in: emp.staffIds}});
                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
                    match.$and.push({'importedBy.id': user.id});
                } else {
                    return [];
                }
            } else {
               return [];
            }
        }

        if (!_.isEmpty(query.posId)) {
            match['pos.id'] = query.posId;
        } else if (!_.isEmpty(query.exchangeId)) {
            match.$and.push({$or: [
                {'pos.id': query.exchangeId},
                {'pos.parentId': query.exchangeId}
            ]});
        }

        if (!_.isEmpty(query.assignedDateFrom)) {
            match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
        }

        if (!_.isEmpty(query.assignedDateTo)) {
            match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
        }

        if (!_.isEmpty(query.resource)) {
            match.source = query.resource;
        } else {
            match.source = {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]};
        }

        if (!_.isEmpty(query['createdFrom']) || !_.isEmpty(query['createdTo'])) {
            match['createdDate'] = {};
            if (!_.isEmpty(query['createdFrom'])) {
                match['createdDate']['$gte'] = new Date(parseInt(query['createdFrom']));
            }
            if (!_.isEmpty(query['createdTo'])) {
                match['createdDate']['$lte'] = new Date(parseInt(query['createdTo']));
            }
        }
        aggregate.push({
            $match: match
        });
        aggregate.push({
            $addFields: {
                callHistoryCount: {
                    $cond: [{ $isArray: '$callHistory' }, {
                        $size: {
                            $filter: {
                                input: "$callHistory",
                                as: "e",
                                cond: { $eq: ["$$e.isCalled", true] }
                            }
                        }
                    }, 0]
                },
                callHistoryMinuteCount: {
                    $cond: [{ $isArray: '$callHistory' }, {
                        $sum: "$callHistory.answerTime"
                    }, 0]
                }
            }
        });
        aggregate.push({
            $group: {
                _id: '$pos.id',
                posName: { $first: '$pos.name' },
                countAll: { $sum: 1 },
                countProcessing: { $sum: { $cond: [{ $eq: ['$lifeCycleStatus', 'primary_processing'] }, 1, 0] } },
                countRemoved: { $sum: { $cond: [{ $eq: ['$lifeCycleStatus', 'removed'] }, 1, 0] } },
                countInValid: {
                    $sum: {
                        $cond: [{
                            $and: [
                                { $eq: ['$lifeCycleStatus', 'primary_processing'] },
                                { $eq: ['$updatedName', ''] },
                                { $eq: ['$updatedEmail', ''] },
                                { $eq: ['$updatedPhone', ''] },
                                { $eq: ['$isInNeed', ''] },
                                { $eq: ['$otherReason', ''] },
                                { $eq: ['$note', ''] },
                                { $eq: ['$needLoan', false] },
                                { $eq: ['$isAppointment', false] },
                                { $eq: ['$isVisited', false] },
                            ]
                        }, 1, 0]
                    }
                },
                countCall: {
                    $sum: '$callHistoryCount'
                },
                countCallMinute: {
                    $sum: '$callHistoryMinuteCount'
                },
                latestUpdate: { $max: '$updatedDate' },
            }
        });
        aggregate.push({
            $facet: {
                rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                totalCount: [
                    {
                        $count: 'count'
                    }
                ]
            }
        });

        return this.readModel.aggregate(aggregate).allowDiskUse(true).exec();
    }

    async getLeadCareByStatus(user, status: ExploitCareEnum, query: any, page: number, pageSize: number) {
        const offset = (page === 1) ? 0 : ((page - 1) * pageSize);

        const conditions: any = {
            exploitStatus: (status === ExploitCareEnum.ASSIGN) ? { $in: [ ExploitCareEnum.ASSIGN, ExploitCareEnum.REASSIGN ] } : status,
            'forBQL': { $ne: true }
        };
        const employee = await this.employeeRepository.findOne({id: user.id});
        if (!user.roles.includes(PermissionConst.LEAD_REPORT_QUERY_ALL_CARE)) {
          if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_PROJECT_CARE)) {
            const projectIds = await this.propertyClient
            .sendDataPromise({ id: user.id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_CUSTOMER_SERVICE);
            conditions['project.id'] = { $in: projectIds }
          } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
              conditions['takeCare.id'] = { $in: employee.staffIds };
          } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
              conditions['importedBy.id'] = user.id;
          } else {
              return [null, 0];
          }
        } else {
          query['importedBy.id'] = { $in: employee?.staffIds || [] };
        }

        if (query.pos) conditions['pos.id'] = query.pos;
        if (query.takeCareId) conditions['takeCare.id'] = query.takeCareId;

        if (query.startDate || query.endDate) conditions.updatedDate = {};
        if (query.startDate) conditions.updatedDate['$gte'] = new Date(parseInt(query.startDate));
        if (query.endDate) conditions.updatedDate['$lte'] = new Date(parseInt(query.endDate));

        return Promise.all([
            this.readModel
                .find(conditions, { _id: 1, id: 1, code: 1, exploitStatus: 1, pos: 1, takeCare: 1, updatedDate: 1 })
                .sort({ updatedDate: 1 })
                .limit(pageSize)
                .skip(offset)
                .exec(),

                this.readModel.countDocuments(conditions),
            ]);
    }

    async getLeadCaresByStatusForExport(user, status: ExploitCareEnum, query: any) {
        const leadCares = [];
        const limit = 10;
        let page = 1;
        let serial = 0;
        
        const conditions: any = {
            exploitStatus: (status === ExploitCareEnum.ASSIGN) ? { $in: [ ExploitCareEnum.ASSIGN, ExploitCareEnum.REASSIGN ] } : status,
        };
        const employee = await this.employeeRepository.findOne({id: user.id});
        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL_CARE)) {
          if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_PROJECT_CARE)) {
            const projectIds = await this.propertyClient
            .sendDataPromise({ id: user.id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_CUSTOMER_SERVICE);
            conditions['project.id'] = { $in: projectIds }
          } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
              conditions['takeCare.id'] = { $in: employee.staffIds };
          } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
              conditions['importedBy.id'] = user.id;
          } else {
              return [null, 0];
          }
        } else {
          query['importedBy.id'] = { $in: employee?.staffIds || [] };
        }
        if (query.pos) conditions['pos.id'] = query.pos;
        if (query.takeCareId) conditions['takeCare.id'] = query.takeCareId;

        if (query.startDate || query.endDate) conditions.updatedDate = {};
        if (query.startDate) conditions.updatedDate['$gte'] = new Date(parseInt(query.startDate));
        if (query.endDate) conditions.updatedDate['$lte'] = new Date(parseInt(query.endDate));

        const total = await this.readModel.countDocuments(conditions);
        const totalPages = Math.ceil(total / limit);

        while (page <= totalPages) {
            const offset = (page === 1) ? 0 : ((page - 1) * limit);

            leadCares.push(
                ...await this.readModel
                    .aggregate([
                        { $match: conditions },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                exploitStatus: 1,
                                pos: '$pos.name',
                                takeCare: '$takeCare.name',
                                updatedDate: 1,
                                surveys: 1,
                            }
                        },
                        { $skip: offset },
                        { $limit: limit },
                    ])
                    .allowDiskUse(true)
                    .exec()
            );

            page++;
        }

        return leadCares.map(item => {
            serial++;

            return {
                serial,
                ...item,
            };
        });
    }

    async createMany(records: ILeadCare[]) {
      return Bluebird.map(
          records,
          async (item) => {
              const doc = await this.readModel.findById(item.id);
              if (doc) {
                  await doc.updateOne(item);
              } else {
                  await this.readModel.create(item);
              }
          },
          { concurrency: 30 }
      );
    }

    async findMany(filter, project = {}, sort = {}, limit = null) {
        return this.readModel.find(filter, project).sort(sort).limit(limit);
    }


    async findAllPrimaryBQL(user, query: any , projectBqlMember:any): Promise<any> {
        const match: any = {
            'forBQL': true
        };
        const matchKeywords: any = {};
        let sort: any = {
            code: 1
        };
        // match.type = CommonConst.TYPE.PRIMARY;
        match.$and = []
    
        if(query.projectId){
            let usr = projectBqlMember.find(item =>{
                return item.id === user.id
            })

            if(usr){
                if(!user.roles.includes(PermissionConst.LEAD_CARE_GET_BY_ADMIN) && !user.roles.includes(PermissionConst.LEAD_CARE_BQL_GET_ALL)) {
                    // Lấy của user đó
                    match.$and.push({ 'takeCare.id': usr.id });
                } else if (!user.roles.includes(PermissionConst.LEAD_CARE_GET_BY_ADMIN) && user.roles.includes(PermissionConst.LEAD_CARE_BQL_GET_ALL)) {
                    // Lấy trang thái Ban quản lý + của user đó
                    match.$and.push({$or: [
                        { 'repoType': LeadRepoCareEnum.BQL },
                        { 'takeCare.id': usr.id }
                    ]});
                }
            } else {
                return []
            }
            match.$and.push({'project.id': query.projectId});
        } else {
            match.$and = [{ 'project.id': { $in: projectBqlMember } }]
        }

        match.$and.push({type: CommonConst.TYPE.PRIMARY});

        if (query) {
            if (!_.isEmpty(query.query)) {
                matchKeywords.$or = [
                    { 'name': { $regex: query.query, $options: 'i' } },
                    { 'phone': { $regex: query.query, $options: 'i' } },
                    { 'code': { $regex: query.query, $options: 'i' } },
                    { 'updatedName': { $regex: query.query, $options: 'i' } },
                    { 'updatedPhone': { $regex: query.query, $options: 'i' } },
                    { 'note': { $regex: query.query, $options: 'i' } },
                ];
            }
            if (!_.isEmpty(query['resource'])) {
                match['resource'] = query['resource'];
            }

            if (!_.isEmpty(query.employeeId)) {
                match.$and.push({processBy: query.employeeId});
            } else if (!_.isEmpty(query.posId)) {
                match.$and.push({'pos.id': query.posId});
            } else if (!_.isEmpty(query.exchangeId)) {
                match.$and.push({$or: [
                    {'pos.id': query.exchangeId},
                    {'pos.parentId': query.exchangeId}
                ]});
            }

            if (!_.isEmpty(query.assignedDateFrom)) {
                match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
            }

            if (!_.isEmpty(query.assignedDateTo)) {
                match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
            }

            if (!_.isEmpty(query.sort)) {
                sort = this.transformSort(query.sort) || {
                    code: 1
                };
            }

            if(query.exploitStatus) {
                const exploitStatus =  query.exploitStatus.split(",")
                match.$and.push({exploitStatus :{ $in: exploitStatus }})
            }
            if(query.repoCode) {
              match.$and.push({repoCode : query.repoCode})
            }
            if(query.type) {
              match.$and.push({repoType : query.type})
            }
        }

        const projection = {
            "id":1,
            "createdBy":1,
            "modifiedDate":1,
            "modifiedBy":1,
            "title":1,
            "customerId":1,
            "name":1,
            "address":1,
            "phone":1,
            "description":1,
            "createdDate":1,
            "updatedDate":1,
            "email":1,
            "notes":1,
            "customer":1,
            "processedDate":1,
            "code":1,
            "assignedDate":1,
            "importedBy":1,
            "exploitStatus":1,
            "takeCare":1,
            "project":1,
            "note":1,
            "repoType":1,
            "repoCode":1,
            "customData":1,
            "dateEndWork":1,
            "idRepoConfig":1,
            "nameRepoConfig":1,
            "rateValue": 1,
            "rateDescription": 1,
            "canSurvey": 1,
            "target": 1,
            "submitSurvey": 1,
            "reason": 1,
            "surveys": 1,
            "surveyAnswers": 1,
            "isRequireSurvey": 1,
        }

        const aggregate: any[] = [
            {
                $match: matchKeywords
            },
            {
                $match: match
            },
            {
                $project: projection
            },
            { $sort: sort },
        ]
        if (!_.isEmpty(query['page']) || !_.isEmpty(query['pageSize'])) {
            const page: number = parseInt(query['page']) || 1;
            const pageSize: number = parseInt(query['pageSize']) || 10;
            aggregate.push({
                $facet: {
                    rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                    totalCount: [
                        {
                            $count: 'count'
                        }
                    ]
                }
            });
        }
        // console.log(JSON.stringify(aggregate));
        return await this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }

    async findLeadCareByIdBql(id: string) {
        return await this.readModel.aggregate(
            [
                {
                    $match: { 
                        id: id,
                        forBQL: true
                    }
                },
                {
                    $lookup: {
                        from: 'lead-repo-cares',
                        localField: 'repoId',
                        foreignField: 'id',
                        as: 'configData'
                    }
                }
            ]).allowDiskUse(true)
            .exec()
            .then((response) => {
                if (response.length > 0) {
                    if  (response[0].configData.length){
                        response[0].configData = response[0].configData[0].configs.find((item) => item.code === response[0].repoConfigCode);
                    } else {
                        response[0].configData = null;
                    }
                    return response[0];
                }
                return null;
            })
            .catch((exp) => {
                return exp;
            });
    }

    async countAll(query: any, actionName = "countAll") {
        this.loggerService.logLocal(
          this.context,
          clc.yellow(`[${actionName}]`),
          query
        );
        delete query.isPaging;
        delete query.page;
        delete query.pageSize;
        return await this.readModel.countDocuments(query).exec();
      }

    async saveAll(models: any[]): Promise<any> {
        return await this.readModel.bulkWrite(
          models.map((model) => {
            return {
              updateOne: {
                filter: { id: model._id },   // Detect Update by id
                update: { $set: {
                    rateValue: model.rateValue,
                    rateDescription: model.rateDescription
                }}
              }
            };
          })
        );
    }
}
