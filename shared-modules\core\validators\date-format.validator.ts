import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';

@ValidatorConstraint({ name: 'IsDateFormat', async: false })
export class DateFormatValidator implements ValidatorConstraintInterface {
  validate(value: any, _args: ValidationArguments) {
    if (typeof value !== 'string') return false;

    // Regex kiểm định dạng dd/mm/yyyy
    const match = /^(\d{2})\/(\d{2})\/(\d{4})$/.exec(value);
    if (!match) return false;

    const [, dd, mm, yyyy] = match;
    const day = +dd, month = +mm, year = +yyyy;

    if (month < 1 || month > 12) return false;

    // Kiểm tra năm nhuận
    const isLeap = (y: number) =>
      (y % 4 === 0 && y % 100 !== 0) || (y % 400 === 0);

    const daysInMonth = [
      31,
      isLeap(year) ? 29 : 28,
      31, 30, 31, 30, 31, 31, 30, 31, 30, 31,
    ];

    return day >= 1 && day <= daysInMonth[month - 1];
  }

  defaultMessage(args: ValidationArguments) {
    return `${args.property} must be a valid date in DD/MM/YYYY format`;
  }
}

/**
 * Decorator để dùng trong DTO
 */
export function IsDateFormat(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: DateFormatValidator,
    });
  };
}
