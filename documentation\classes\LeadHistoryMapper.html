<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>LeadHistoryMapper</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/mapper/lead-history.mapper.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#leadResponse">leadResponse</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#timezoneclient">timezoneclient</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#convertDate">convertDate</a>
                            </li>
                            <li>
                                <a href="#enitiesToDtos">enitiesToDtos</a>
                            </li>
                            <li>
                                <a href="#enityToDto">enityToDto</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(timezoneclient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="65" class="link-to-prism">src/modules/shared/mapper/lead-history.mapper.ts:65</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>timezoneclient</td>
                                                  
                                                        <td>
                                                                        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="leadResponse"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                            leadResponse</b>
                            <a href="#leadResponse"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    id: &#x27;id&#x27;,
    surveyCode: &#x27;surveyCode&#x27;,
    code: &#x27;code&#x27;,
    name: &#x27;name&#x27;,
    phone: &#x27;phone&#x27;,
    email: &#x27;email&#x27;,
    address: &#x27;address&#x27;,
    description: &#x27;description&#x27;,
    pos: {
      key: &#x27;pos&#x27;,
      transform: (value: any) &#x3D;&gt; {
        if (isNullOrUndefined(value)) return;
        return { id: value.id, name: value.name };
      }
    },
    status: [
      {
        key: &#x27;status&#x27;
      },
      {
        key: &#x27;periodStatus&#x27;,
        transform: (value &#x3D;&gt; {
          let rs &#x3D; &#x27;&#x27;;
          if (isNullOrUndefined(value)) return;
          switch (value) {
            case StatusEnum.GREEN:
              rs &#x3D; &#x27;Mới&#x27;;
              break;
            case StatusEnum.YELLOW:
              rs &#x3D; &#x27;Chờ&#x27;;
              break;
            case StatusEnum.RED:
              rs &#x3D; &#x27;Ưu tiên&#x27;;
              break;
            default:
              break;
          }
          return rs;
        })
      }
    ],
    lifeCycleStatus: &#x27;lifeCycleStatus&#x27;,
    processBy: &#x27;processBy&#x27;,
    type: &#x27;type&#x27;,
    customerId: &#x27;customerId&#x27;,
    decisionDate: &#x27;decisionDate&#x27;,
    // Base
    createdDate: {
      key: &#x27;createdDate&#x27;,
      transform: (value &#x3D;&gt; this.convertDate(value))
    },
    updatedDate: {
      key: &#x27;updatedDate&#x27;,
      transform: (value &#x3D;&gt; this.convertDate(value))
    }
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="9" class="link-to-prism">src/modules/shared/mapper/lead-history.mapper.ts:9</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="timezoneclient"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                            timezoneclient</b>
                            <a href="#timezoneclient"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="8" class="link-to-prism">src/modules/shared/mapper/lead-history.mapper.ts:8</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="convertDate"></a>
                    <span class="name">
                        <b>
                            convertDate
                        </b>
                        <a href="#convertDate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>convertDate(value)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="72"
                            class="link-to-prism">src/modules/shared/mapper/lead-history.mapper.ts:72</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>value</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="enitiesToDtos"></a>
                    <span class="name">
                        <b>
                            enitiesToDtos
                        </b>
                        <a href="#enitiesToDtos"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>enitiesToDtos(entities)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="81"
                            class="link-to-prism">src/modules/shared/mapper/lead-history.mapper.ts:81</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>entities</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="enityToDto"></a>
                    <span class="name">
                        <b>
                            enityToDto
                        </b>
                        <a href="#enityToDto"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>enityToDto(entity)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="77"
                            class="link-to-prism">src/modules/shared/mapper/lead-history.mapper.ts:77</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>entity</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { isNullOrUndefined } from &#x27;util&#x27;;
import { StatusEnum } from &#x27;../enum/status.enum&#x27;;

import moment &#x3D; require(&#x27;moment-timezone&#x27;);
import objectMapper &#x3D; require(&#x27;object-mapper&#x27;);

export class LeadHistoryMapper {
  private timezoneclient: string;
  private leadResponse: any &#x3D; {
    id: &#x27;id&#x27;,
    surveyCode: &#x27;surveyCode&#x27;,
    code: &#x27;code&#x27;,
    name: &#x27;name&#x27;,
    phone: &#x27;phone&#x27;,
    email: &#x27;email&#x27;,
    address: &#x27;address&#x27;,
    description: &#x27;description&#x27;,
    pos: {
      key: &#x27;pos&#x27;,
      transform: (value: any) &#x3D;&gt; {
        if (isNullOrUndefined(value)) return;
        return { id: value.id, name: value.name };
      }
    },
    status: [
      {
        key: &#x27;status&#x27;
      },
      {
        key: &#x27;periodStatus&#x27;,
        transform: (value &#x3D;&gt; {
          let rs &#x3D; &#x27;&#x27;;
          if (isNullOrUndefined(value)) return;
          switch (value) {
            case StatusEnum.GREEN:
              rs &#x3D; &#x27;Mới&#x27;;
              break;
            case StatusEnum.YELLOW:
              rs &#x3D; &#x27;Chờ&#x27;;
              break;
            case StatusEnum.RED:
              rs &#x3D; &#x27;Ưu tiên&#x27;;
              break;
            default:
              break;
          }
          return rs;
        })
      }
    ],
    lifeCycleStatus: &#x27;lifeCycleStatus&#x27;,
    processBy: &#x27;processBy&#x27;,
    type: &#x27;type&#x27;,
    customerId: &#x27;customerId&#x27;,
    decisionDate: &#x27;decisionDate&#x27;,
    // Base
    createdDate: {
      key: &#x27;createdDate&#x27;,
      transform: (value &#x3D;&gt; this.convertDate(value))
    },
    updatedDate: {
      key: &#x27;updatedDate&#x27;,
      transform: (value &#x3D;&gt; this.convertDate(value))
    }
  };

  constructor(timezoneclient: string) {
    this.timezoneclient &#x3D; timezoneclient;
    process.env.timezone &#x3D; timezoneclient;
  }

  convertDate(value) {
    if (isNullOrUndefined(value)) return;
    return moment(value).tz(this.timezoneclient).format();
  }

  enityToDto(entity) {
    return objectMapper(entity, this.leadResponse);
  }

  enitiesToDtos(entities) {
    const dtos &#x3D; [];
    if (isNullOrUndefined(entities)) {
      return;
    }
    entities.forEach(entity &#x3D;&gt; {
      dtos.push(this.enityToDto(entity));
    });
    return dtos;
  }

}</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadHistoryMapper.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
