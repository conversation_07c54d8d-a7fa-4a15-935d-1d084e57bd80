import { Inject, Injectable, Optional, Scope } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClientFactory, IHttpClient } from './http-client.factory';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';

@Injectable({ scope: Scope.REQUEST })
export class CustomHttpService {
  private httpClient: IHttpClient;
  private request: Request;

  constructor(
    @Inject('HTTP_SERVICE') private readonly http: any,
    @Optional() @Inject(REQUEST) request: any
  ) {
    this.httpClient = HttpClientFactory.createHttpClient(http);
    this.request = request;
  }

  private getAuthorizationHeader(): string | undefined {
    if (this.request && this.request.headers) {
      return this.request.headers.authorization;
    }
    return undefined;
  }

  private mergeConfig(config?: any): any {
    const authorization = this.getAuthorizationHeader();
    if (!authorization) {
      return config;
    }

    const headers = {
      ...(config?.headers || {}),
      Authorization: authorization
    };

    return {
      ...config,
      headers
    };
  }

  get<T>(url: string, config?: any): Observable<T> {
    const mergedConfig = this.mergeConfig(config);
    return this.httpClient.get<T>(url, mergedConfig).pipe(
      map(response => response.data)
    );
  }

  post<T, R = any>(url: string, body: T, config?: any): Observable<R> {
    const mergedConfig = this.mergeConfig(config);
    return this.httpClient.post<T, R>(url, body, mergedConfig).pipe(
      map(response => response.data)
    );
  }

  put<T, R = any>(url: string, body: T, config?: any): Observable<R> {
    const mergedConfig = this.mergeConfig(config);
    return this.httpClient.put<T, R>(url, body, mergedConfig).pipe(
      map(response => response.data)
    );
  }

  delete<T>(url: string, config?: any): Observable<T> {
    const mergedConfig = this.mergeConfig(config);
    return this.httpClient.delete<T>(url, mergedConfig).pipe(
      map(response => response.data)
    );
  }

  async getAsync<T>(url: string, config?: any): Promise<T> {
    const mergedConfig = this.mergeConfig(config);
    const response = await this.toPromise(this.httpClient.get<T>(url, mergedConfig));
    return response.data;
  }

  async postAsync<T, R = any>(url: string, body: T, config?: any): Promise<R> {
    const mergedConfig = this.mergeConfig(config);
    const response = await this.toPromise(this.httpClient.post<T, R>(url, body, mergedConfig));
    return response.data;
  }

  async putAsync<T, R = any>(url: string, body: T, config?: any): Promise<R> {
    const mergedConfig = this.mergeConfig(config);
    const response = await this.toPromise(this.httpClient.put<T, R>(url, body, mergedConfig));
    return response.data;
  }

  async deleteAsync<T>(url: string, config?: any): Promise<T> {
    const mergedConfig = this.mergeConfig(config);
    const response = await this.toPromise(this.httpClient.delete<T>(url, mergedConfig));
    return response.data;
  }

  // Helper method to handle both toPromise() and lastValueFrom()
  private toPromise(observable: Observable<any>): Promise<any> {
    // Check if toPromise exists (NestJS v6)
    if (observable['toPromise'] && typeof observable['toPromise'] === 'function') {
      return observable['toPromise']();
    }

    // For NestJS v10, use lastValueFrom
    try {
      // Dynamically import lastValueFrom to avoid dependency issues
      const rxjs = require('rxjs');
      if (rxjs.lastValueFrom) {
        return rxjs.lastValueFrom(observable);
      }
    } catch (e) {
      console.error('Failed to import lastValueFrom', e);
    }

    // Fallback
    return new Promise((resolve, reject) => {
      observable.subscribe({
        next: value => resolve(value),
        error: err => reject(err),
        complete: () => { }
      });
    });
  }
}
