import { CmdPatternConst } from '../../constant/cross.service.pattern.const';
import * as _ from 'lodash';

export interface AccountInfo {
  username: string;
  fullName: string;
}

export interface AccountMappingOptions {
  includeCreatedBy?: boolean;
  includeModifiedBy?: boolean;
  includeUpdatedBy?: boolean;
  includeProcessBy?: boolean;
  replaceIds?: boolean;
}

export interface AccountMappingResult {
  createdByObj?: AccountInfo;
  updatedByObj?: AccountInfo;
  modifiedByObj?: AccountInfo;
  processByObj?: AccountInfo;
  createdBy?: string;
  modifiedBy?: string;
  updatedBy?: string;
  processBy?: string;
}

export class AccountInfoUtils {
  static async mapSingleAccountInfo(
    item: any,
    stsClient: any,
    options: AccountMappingOptions = {
      includeCreatedBy: true,
      includeModifiedBy: false,
      includeUpdatedBy: false,
      includeProcessBy: false,
      replaceIds: true,
    }
  ): Promise<AccountMappingResult> {
    const mapped = await this.mapAccountInfo([item], stsClient, options);
    return mapped[0];
  }

  static async mapAccountInfo(
    rows: any[],
    stsClient: any,
    options: {
      includeCreatedBy?: boolean;
      includeModifiedBy?: boolean;
      includeUpdatedBy?: boolean;
      includeProcessBy?: boolean,
      includeUploadBy?: boolean,
      replaceIds?: boolean;
    } = {
        includeCreatedBy: true,
        includeModifiedBy: false,
        includeUpdatedBy: false,
        includeProcessBy: false,
        includeUploadBy: false,
        replaceIds: true,
      }
  ): Promise<any[]> {
    if (!rows?.length) return rows;

    // Collect all unique account IDs
    const accountFields = [];
    if (options.includeCreatedBy) accountFields.push('createdBy');
    if (options.includeModifiedBy) accountFields.push('modifiedBy');
    if (options.includeUpdatedBy) accountFields.push('updatedBy');
    if (options.includeProcessBy) accountFields.push('processBy');
    if (options.includeUploadBy) accountFields.push('uploadBy');

    const uniqueAccountIds: Set<string> = new Set(
      _.flatten(rows.map(row => accountFields.map(field => row[field]).filter(Boolean)))
    );

    if (!uniqueAccountIds.size) return rows;

    // Get account info from STS
    const accInfo = await stsClient.sendDataPromise(
      { id: { $in: Array.from(uniqueAccountIds) } },
      CmdPatternConst.STS.ACCOUNT.GET_INFO
    );

    // Map account info to rows
    return rows.map(row => {
      const result: AccountMappingResult = { ...row };

      accountFields.forEach(field => {
        const accountId = row[field];
        if (!accountId || !accInfo.info[accountId]) return;

        // Add object with account details
        const objField = `${field}Obj`;
        result[objField] = {
          username: accInfo.info[accountId].username,
          fullName: accInfo.info[accountId].fullName
        };

        // Replace ID with email if requested
        if (options.replaceIds) {
          result[field] = accInfo.listEmailById[accountId];
        }
      });

      return result;
    });
  }
}
