import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { notificationDto, notificationPayloadDto } from './noti.dto';

@Injectable()
export class NotiService {
  private readonly context = NotiService.name;

  constructor(private readonly configService: ConfigService) {}

  // gửi email
  async push(data: notificationPayloadDto) {
    const notification = this.buildNotification(data);

    const apiUrl = this.configService.get('PUSH_NOTI_URL');
    try {
      await axios.post(apiUrl, notification, {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      throw new Error(error?.message || 'Push notification failed');
    }
  }

  async pushMultiple(datas: notificationPayloadDto[]) {
    const notifications: notificationDto[] = datas.map((payload) =>
      this.buildNotification(payload),
    );

    const apiUrl = this.configService.get('PUSH_MULTIPLE_NOTI_URL');
    try {
      await axios.post(apiUrl, notifications, {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      throw new Error(error?.message || 'Push multiple notifications failed');
    }
  }

  private buildNotification(data: notificationPayloadDto): notificationDto {
    return Object.assign(new notificationDto(), {
      type: 'email',
      persistence: false,
      payload: Object.assign(new notificationPayloadDto(), {
        domain: 'CRM_1000',
        autoSendWhenFail: false,
        cancelSendDay: 0,
        sender: '<EMAIL>',
        subject: data.subject,
        content: data.content,
        receives: data.receives || [],
        ccReceives: data.ccReceives || [],
        bccReceives: data.bccReceives || [],
      }),
    });
  }

  async pushNoti(data: notificationDto) {
    const apiUrl = this.configService.get('PUSH_NOTI_URL');
    try {
      await axios.post(apiUrl, data, {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      throw new Error(error?.message || 'Push notification failed');
    }
  }

  async pushNotiMultiple(datas: notificationDto[]) {
    const apiUrl = this.configService.get('PUSH_MULTIPLE_NOTI_URL');
    try {
      await axios.post(apiUrl, datas, {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      throw new Error(error?.message || 'Push multiple notifications failed');
    }
  }
}
