import { Document } from 'mongoose';
export interface ICostItem {
  budgetCode: string;
  longText: string;
  budgetCMCode: string;
  budgetDesciption1: string;
  budgetDesciption2: string;
  budgetActivity: string;
  budgetActivityName: string;
  budgetCategory: string;
  budgetCost: string;
  budgetCostName: string;
  budgetCosting: string;
  budgetDivision: string;
  budgetDivisionName: string;
  budgetLevel: string;
  budgetSupper: string;
  budgetCMSupper: string;
  check: string;
  budgetCostcenter: string;
  status: number;
  createdDate?: Date;
  lastUpdate: Date;
}

export interface ICostItemDocument extends Document, ICostItem {
  id: string;
}
