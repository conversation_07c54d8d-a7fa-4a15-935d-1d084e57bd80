import { Model } from "mongoose";
import { Inject, Injectable } from "@nestjs/common";
import { QueryAggregateModel } from "../models/query-aggregate.model";
import { ILeadDocument } from "../interfaces/document.interface";
import _ = require("lodash");
import { LifeCycleStatusEnum } from "../../shared/enum/life-cycle-status.enum";
import { SourceEnum } from "../../shared/enum/source.enum";
import { isNullOrUndefined } from "util";
import { CommonConst } from "../../../modules/shared/constant/common.const";
import { TransactionTypeEnum } from "../../../modules/shared/enum/transaction-type.enum";
import { EmployeeQueryRepository } from "../../employee/repository/query.repository";
import { PermissionConst } from "../../../modules/shared/constant/permission.const";
import { ExploitEnum } from "../../shared/enum/exploit.enum";
import { ILead } from "../../shared/services/lead/interfaces/lead.interface";
import * as Bluebird from "bluebird";
import * as moment from "moment";
import { OrgchartClient } from "../../mgs-sender/orgchart.client";
import { CmdPatternConst } from "../../shared/constant/cmd-pattern.const";
import {
  AwesomeLogger,
  CmdPatternConst as cmd2,
} from "../../../../shared-modules";
import { EmployeeClient } from "../../mgs-sender/employee.client";
import { checkPermission } from "../../shared/utils/checkPermission";
import { StsClient } from "../../mgs-sender/sts.client";

const validReportStatus = [ExploitEnum.DONE, ExploitEnum.CANCEL];

@Injectable()
export class LeadQueryRepository {
  private readonly logger = new AwesomeLogger(LeadQueryRepository.name);

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<ILeadDocument>,
    private readonly employeeRepository: EmployeeQueryRepository,
    private readonly orgchartClient: OrgchartClient
  ) { }


  /**
   *
   * @param page
   * @param pageSize
   * @param query
   */
  async listAllCommon(
    user: any,
    page: number = 1,
    pageSize: number = 10,
    query: any = {}
  ): Promise<ILeadDocument[]> {
    let sort: any = { updatedDate: -1 };
    const match: any = {};
    const agg = [];

    // filter by creator
    match.$or = [
      { createdBy: user.id },
      { "importedBy.id": user.id }
    ];

    // vdl
    user?.dataPermission
      .filter(item => item.dataType === 'INTERNAL_ORGCHART')
      .forEach(item => {
        match.$or.push({
          $expr: {
            $eq: [
              { $toLower: { $substr: ["$orgCode", 0, 4] } },
              item.companyCode.substring(0, 4).toLowerCase()
            ]
          }
        });
      });

    // map lead source
    if (!_.isEmpty(query.source)) {
      match.source = query.source;
    }

    // default PRIMARY
    if (!_.isEmpty(query.type)) {
      match.type = { $in: query.type.split(",") };
    }

    let startDate = 0;
    let endDate = 0;
    if (query["createdFrom"] && query["createdTo"]) {
      startDate = Number(new Date(query["createdFrom"]));
      endDate = Number(new Date(query["createdTo"])) + 86399999;
      match.createdDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    } else if (query["createdFrom"]) {
      startDate = Number(new Date(query["createdFrom"]));
      match.createdDate = { $gte: new Date(startDate) };
    } else if (query["createdTo"]) {
      endDate = Number(new Date(query["createdTo"])) + 86399999;
      match.createdDate = { $lte: new Date(endDate) };
    }

    if (!_.isEmpty(query.isHot)) {
      match.isHot = query.isHot === "true";
    }

    // let matchKeywords: any = {};
    if (!_.isEmpty(query.search)) {
      match.$or.push(...[
        { code: { $regex: query.search, $options: "i" } },
        { name: { $regex: query.search, $options: "i" } },
        { phone: { $regex: query.search, $options: "i" } },
      ]);
    }

    if (!_.isEmpty(query.sort)) {
      sort = this.transformSort(query.sort) || {
        updatedDate: -1,
      };
    } else {
      sort = { updatedDate: -1 };
    }

    agg.push({ $match: match });

    agg.push({
      $lookup: {
        from: "employees",
        localField: "processBy",
        foreignField: "id",
        as: "employeeTakeCare",
      },
    });

    // agg.push({
    //   $addFields: {
    //     callHistoryCount: {
    //       $cond: [
    //         { $isArray: "$callHistory" },
    //         {
    //           $size: {
    //             $filter: {
    //               input: "$callHistory",
    //               as: "e",
    //               cond: { $eq: ["$$e.isCalled", true] },
    //             },
    //           },
    //         },
    //         0,
    //       ],
    //     },
    //     callHistoryMinuteCount: {
    //       $cond: [
    //         { $isArray: "$callHistory" },
    //         {
    //           $sum: "$callHistory.answerTime",
    //         },
    //         0,
    //       ],
    //     },
    //   },
    // });

    // mapping repo
    agg.push(
      {
        $lookup: {
          from: "lead-repos",
          localField: "repoId",
          foreignField: "id",
          as: "leadRepo",
        },
      },
      {
        $addFields: {
          repo: {
            id: { $arrayElemAt: ["$leadRepo.id", 0] },
            name: { $arrayElemAt: ["$leadRepo.name", 0] },
            code: { $arrayElemAt: ["$leadRepo.code", 0] },
            config: {
              $arrayElemAt: [
                {
                  $cond: {
                    if: { $eq: ["$isHot", true] },
                    then: [{ code: "", name: "" }],
                    else: {
                      $map: {
                        input: {
                          $filter: {
                            input: { $arrayElemAt: ["$leadRepo.configs", 0] },
                            as: "config",
                            cond: { $eq: ["$repoConfigCode", "$$config.code"] },
                          },
                        },
                        as: "config",
                        in: {
                          code: "$$config.code",
                          name: "$$config.name",
                        },
                      },
                    },
                  },
                },
                0,
              ],
            },
          },
        },
      }
    );

    this.logger.debug("match: ", JSON.stringify(match));
    this.logger.debug("sort: ", JSON.stringify(sort));

    agg.push(
      {
        $sort: sort,
      },
      {
        $facet: {
          rows: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          totalCount: [
            {
              $count: "count",
            },
          ],
        },
      }
    );

    return await this.readModel.aggregate(agg).allowDiskUse(true).exec();
  }

  async findAllPrimary(user, query: any): Promise<any> {
    const match: any = {};
    const matchKeywords: any = {};
    let sort: any = { updatedDate: -1 };
    const agg = [];
    const orCond = [];
    match.$and = [];
    match.$and.push({ type: CommonConst.TYPE.PRIMARY });
    match.$and.push({
      exploitStatus: {
        $in: [
          ExploitEnum.MANUAL_DELIVER,
          ExploitEnum.ASSIGN,
          ExploitEnum.PROCESSING,
          ExploitEnum.DONE,
          ExploitEnum.CANCEL
        ]
      }
    });
    let lstPosId: any[] = [];
    let employeeLogIn: any;
    const emp = await this.employeeRepository.findOne({
      "account.id": user.id,
    });
    employeeLogIn = emp[0];
    if (employeeLogIn?.isLinemanager) {
      // if (employeeLogIn.pos?.id) {
      //   const org = await this.orgchartClient.sendDataPromise({ id: employeeLogIn.pos?.id }, cmd2.ORGCHART.LISTENER.GET_BY_QUERY);
      //   console.log('org', org);
      //   match["takeCare.id"] = { $in: org ? org.staffIds : [] };
      // } else
      if (employeeLogIn.orgCode) {
        const org = await this.orgchartClient.sendDataPromise({ code: employeeLogIn.orgCode }, cmd2.ORGCHART.LISTENER.GET_BY_QUERY);
        match["takeCare.id"] = { $in: org ? org.staffIds : [] };
      } else {
        return [];
      }
    } else {
      match["takeCare.id"] = employeeLogIn.id;
    }

    // vdl
    user?.dataPermission
      .filter(item => item.dataType === 'INTERNAL_ORGCHART')
      .forEach(item => {
        orCond.push({
          $expr: {
            $eq: [
              { $toLower: { $substr: ["$orgCode", 0, 4] } },
              item.companyCode.substring(0, 4).toLowerCase()
            ]
          }
        });
      });

    if (query) {
      if (!_.isEmpty(query.search)) {
        orCond.push(...[
          { name: { $regex: query.search, $options: "i" } },
          { phone: { $regex: query.search, $options: "i" } },
          { code: { $regex: query.search, $options: "i" } },
        ]);
      }
      if (!_.isEmpty(query["source"])) {
        match["source"] = query["source"];
      }

      if (!_.isEmpty(query.isHot)) {
        match.isHot = query.isHot === "true";
      }

      if (!_.isEmpty(query.sort)) {
        sort = this.transformSort(query.sort) || {
          code: 1,
        };
      }

      let startDate = 0;
      let endDate = 0;
      if (query["createdFrom"] && query["createdTo"]) {
        startDate = Number(new Date(query["createdFrom"]));
        endDate = Number(new Date(query["createdTo"])) + 86399999;
        match.createdDate = {
          $gte: new Date(startDate),
          $lte: new Date(endDate),
        };
      } else if (query["createdFrom"]) {
        startDate = Number(new Date(query["createdFrom"]));
        match.createdDate = { $gte: new Date(startDate) };
      } else if (query["createdTo"]) {
        endDate = Number(new Date(query["createdTo"])) + 86399999;
        match.createdDate = { $lte: new Date(endDate) };
      }

      if (query["exploitStatus"]) {
        match.$and.push({
          exploitStatus: query["exploitStatus"],
        });
      }
    }

    console.log('match', JSON.stringify(match));

    if (orCond.length > 0) {
      matchKeywords.$or = orCond;
    }

    // mapping repo
    agg.push(
      {
        $lookup: {
          from: "lead-repos",
          localField: "repoId",
          foreignField: "id",
          as: "leadRepo",
        },
      },
      {
        $addFields: {
          repo: {
            id: { $arrayElemAt: ["$leadRepo.id", 0] },
            name: { $arrayElemAt: ["$leadRepo.name", 0] },
            code: { $arrayElemAt: ["$leadRepo.code", 0] },
            config: {
              $arrayElemAt: [
                {
                  $cond: {
                    if: { $eq: ["$isHot", true] },
                    then: [{ code: "", name: "" }],
                    else: {
                      $map: {
                        input: {
                          $filter: {
                            input: { $arrayElemAt: ["$leadRepo.configs", 0] },
                            as: "config",
                            cond: { $eq: ["$repoConfigCode", "$$config.code"] },
                          },
                        },
                        as: "config",
                        in: {
                          code: "$$config.code",
                          name: "$$config.name",
                          visiblePhone: "$$config.visiblePhone",
                        },
                      },
                    },
                  },
                },
                0,
              ],
            },
          },
        },
      }
    );

    agg.push(
      {
        $match: matchKeywords,
      },
      {
        $match: match,
      },
      { $sort: sort }
    );
    if (!_.isEmpty(query["page"]) || !_.isEmpty(query["pageSize"])) {
      const page: number = parseInt(query["page"]) || 1;
      const pageSize: number = parseInt(query["pageSize"]) || 10;
      agg.push({
        $facet: {
          rows: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          totalCount: [
            {
              $count: "count",
            },
          ],
        },
      });
    }
    return await this.readModel.aggregate(agg).allowDiskUse(true).exec();
  }

  async getLeadsToDeliver(
    user: any,
    page: number = 1,
    pageSize: number = 10,
    query: any = {}
  ): Promise<ILeadDocument[]> {
    let sort: any = { updatedDate: -1 };
    const match: any = {};
    const agg = [];
    const orCond = [];
    match.$and = [{ exploitStatus: ExploitEnum.MANUAL_DELIVER }];

    const emp = await this.employeeRepository.findOne({
      "account.id": user.id,
    });
    const employeeLogIn = emp[0];
    if (employeeLogIn?.isLinemanager) {
      // if (employeeLogIn.pos?.id) {
      //   match["pos.id"] = employeeLogIn.pos?.id;
      // } else 
      if (employeeLogIn.orgCode) {
        const org = await this.orgchartClient.sendDataPromise({ code: employeeLogIn.orgCode }, cmd2.ORGCHART.LISTENER.GET_BY_QUERY);
        orCond.push({ "pos.id": org ? org.id : '' });
      } else {
        return [];
      }
    } else {
      return [];
    }

    // vdl
    user?.dataPermission
      .filter(item => item.dataType === 'INTERNAL_ORGCHART')
      .forEach(item => {
        orCond.push({
          $expr: {
            $eq: [
              { $toLower: { $substr: ["$orgCode", 0, 4] } },
              item.companyCode.substring(0, 4).toLowerCase()
            ]
          }
        });
      });

    if (!_.isEmpty(query.statusLead)) {
      match.$and.push({
        exploitStatus: {
          $in: query.statusLead,
        },
      });
    }

    if (!_.isEmpty(query.source)) {
      match.source = query.source;
    } else {
      match.source = {
        $nin: [SourceEnum.DangTin, SourceEnum.YeuCau],
      };
    }
    if (!_.isEmpty(query.type)) {
      match.type = { $in: query.type.split(",") };
    }
    // if (!_.isEmpty(query.posId)) {
    //   match["pos.id"] = query.posId;
    // }

    // if (!_.isEmpty(query.exchangeId)) {
    //   match.$and.push({
    //     $or: [
    //       { "pos.id": query.exchangeId },
    //       { "pos.parentId": query.exchangeId },
    //     ],
    //   });
    // }

    if (!_.isEmpty(query.assignedDateFrom)) {
      match.$and.push({
        assignedDate: { $gte: new Date(parseInt(query.assignedDateFrom, 10)) },
      });
    }

    if (!_.isEmpty(query.assignedDateTo)) {
      match.$and.push({
        assignedDate: { $lte: new Date(parseInt(query.assignedDateTo, 10)) },
      });
    }

    let startDate = 0;
    let endDate = 0;
    if (query["createdFrom"] && query["createdTo"]) {
      startDate = Number(new Date(query["createdFrom"]));
      endDate = Number(new Date(query["createdTo"])) + 86399999;
      match.createdDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    } else if (query["createdFrom"]) {
      startDate = Number(new Date(query["createdFrom"]));
      match.createdDate = { $gte: new Date(startDate) };
    } else if (query["createdTo"]) {
      endDate = Number(new Date(query["createdTo"])) + 86399999;
      match.createdDate = { $lte: new Date(endDate) };
    }

    if (!_.isEmpty(query.lifeCycleStatus)) {
      match.lifeCycleStatus = { $in: query.lifeCycleStatus.split(",") };
    } else {
      agg.push({
        $lookup: {
          from: "employees",
          localField: "processBy",
          foreignField: "id",
          as: "employeeTakeCare",
        },
      });
    }
    if (!_.isEmpty(query.isHot)) {
      match.isHot = query.isHot === "true";
    }

    let matchKeywords: any = {};
    if (!_.isEmpty(query.search)) {
      orCond.push(...[
        { name: { $regex: query.search, $options: "i" } },
        { phone: { $regex: query.search, $options: "i" } },
        { code: { $regex: query.search, $options: "i" } }
      ]);
    }
    if (!_.isEmpty(query.sort)) {
      sort = this.transformSort(query.sort) || { createdDate: -1 };
    }

    console.log('match', match);

    if (orCond.length > 0) {
      matchKeywords.$or = orCond;
    }

    console.log('matchKeywords', matchKeywords);
    // mapping repo
    agg.push(
      {
        $lookup: {
          from: "lead-repos",
          localField: "repoId",
          foreignField: "id",
          as: "leadRepo",
        },
      },
      {
        $addFields: {
          repo: {
            id: { $arrayElemAt: ["$leadRepo.id", 0] },
            name: { $arrayElemAt: ["$leadRepo.name", 0] },
            code: { $arrayElemAt: ["$leadRepo.code", 0] },
            config: {
              $arrayElemAt: [
                {
                  $cond: {
                    if: { $eq: ["$isHot", true] },
                    then: [{ code: "", name: "" }],
                    else: {
                      $map: {
                        input: {
                          $filter: {
                            input: { $arrayElemAt: ["$leadRepo.configs", 0] },
                            as: "config",
                            cond: { $eq: ["$repoConfigCode", "$$config.code"] },
                          },
                        },
                        as: "config",
                        in: {
                          code: "$$config.code",
                          name: "$$config.name",
                        },
                      },
                    },
                  },
                },
                0,
              ],
            },
          },
        },
      }
    );

    agg.push(
      {
        $match: match,
      },
      {
        $match: matchKeywords,
      },
      {
        $sort: sort,
      },
      {
        $project: {
          id: 1,
          project: 1,
          source: 1,
          code: 1,
          description: 1,
          email: 1,
          phone: 1,
          name: 1,
          note: 1,
          profileUrl: 1,
          createdBy: 1,
          createdDate: 1,
          updatedDate: 1,
          modifiedBy: 1,
          assignedDate: 1,
          countAssign: 1,
          exploitStatus: 1,
          isHot: 1,
          takeCare: 1,
          repo: 1,
        },
      },
      {
        $facet: {
          rows: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          totalCount: [
            {
              $count: "count",
            },
          ],
        },
      }
    );

    return await this.readModel.aggregate(agg).allowDiskUse(true).exec();
  }

  async findAll(): Promise<ILeadDocument[]> {
    return await this.readModel
      .find()
      .exec()
      .then((result) => {
        return result;
      });
  }

  async findAllCommon(query): Promise<ILeadDocument[]> {
    const match: any = {
      source: {
        $nin: [SourceEnum.DangTin, SourceEnum.YeuCau],
      },
      type: query.type,
    };
    if (!_.isEmpty(query.keywords)) {
      match["name"] = { $regex: new RegExp(query.keywords), $options: "i" };
    }
    return await this.readModel
      .aggregate([
        {
          $match: match,
        },
        {
          $lookup: {
            from: "employees",
            localField: "processBy",
            foreignField: "id",
            as: "employeeTakeCare",
          },
        },
      ])
      .allowDiskUse(true)
      .exec();
  }

  async findAllAdvising(): Promise<ILeadDocument[]> {
    return await this.readModel
      .aggregate([
        {
          $match: {
            type: CommonConst.TYPE.ADVISING,
          },
        },
        {
          $lookup: {
            from: "employees",
            localField: "processBy",
            foreignField: "id",
            as: "employeeTakeCare",
          },
        },
      ])
      .allowDiskUse(true)
      .exec();
  }

  async countIsUsedInLead(name: string): Promise<number> {
    return await this.readModel
      .countDocuments({
        source: { $regex: new RegExp(name, "i") },
      })
      .exec();
  }



  async getByCustomer(customerId) {
    return await this.readModel
      .find({ customerId })
      .sort({ createdDate: -1 })
      .exec()
      .then((result) => {
        return result;
      });
  }

  async getAllByPos(posId: String): Promise<ILeadDocument[]> {
    return await this.readModel
      .aggregate([
        { $match: { "pos.id": posId } },
        { $sort: { createDate: 1 } },
      ])
      .allowDiskUse(true)
      .exec()
      .then((rs) => {
        return rs;
      })
      .catch((err) => {
        return err;
      });
  }

  async findOne(query): Promise<ILeadDocument> {
    return await this.readModel
      .findOne(query)
      .exec()
      .then((result) => {
        return result;
      });
  }

  async findLeadById(id: string): Promise<ILeadDocument> {
    return await this.readModel
      .aggregate([
        {
          $match: { id },
        },
        {
          $lookup: {
            from: `${CommonConst.LEAD_REPO_COLLECTION}s`,
            localField: "repoId",
            foreignField: "id",
            as: "configData",
          },
        },
        {
          $addFields: {
            repo: {
              id: { $arrayElemAt: ["$configData.id", 0] },
              name: { $arrayElemAt: ["$configData.name", 0] },
              code: { $arrayElemAt: ["$configData.code", 0] },
              config: {
                $arrayElemAt: [
                  {
                    $cond: {
                      if: { $eq: ["$isHot", true] },
                      then: [{ code: "", name: "" }],
                      else: {
                        $map: {
                          input: {
                            $filter: {
                              input: {
                                $arrayElemAt: ["$configData.configs", 0],
                              },
                              as: "config",
                              cond: {
                                $eq: ["$repoConfigCode", "$$config.code"],
                              },
                            },
                          },
                          as: "config",
                          in: {
                            code: "$$config.code",
                            name: "$$config.name",
                          },
                        },
                      },
                    },
                  },
                  0,
                ],
              },
            },
          },
        },
      ])
      .allowDiskUse(true)
      .exec()
      .then((response) => {
        if (response.length > 0) {
          if (response[0].configData.length) {
            response[0].configData = response[0].configData[0].configs.find(
              (item) => item.code === response[0].repoConfigCode
            );
            response[0].visiblePhone = response[0].configData?.visiblePhone;
          } else {
            response[0].configData = null;
          }
          return response[0];
        }
        return null;
      })
      .catch((exp) => {
        return exp;
      });
  }

  async findAggregateModelById(id: string): Promise<QueryAggregateModel> {
    return await this.readModel
      .findOne({ id })
      .exec()
      .then((response) => {
        console.log("findAggregateModelById lead query side");
        return new QueryAggregateModel(id);
      })
      .catch((exp) => {
        return exp;
      });
  }

  async create(readmodel): Promise<ILeadDocument> {
    return await this.readModel
      .create(readmodel)
      .then((response) => {
        console.log("createEvent Lead at query side");
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async update(model): Promise<ILeadDocument> {
    model.updatedDate = Date.now();
    return await this.readModel
      .update({ id: model.id }, model)
      .then((response) => {
        console.log("update Lead at query side");
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async updateByCode(model): Promise<ILeadDocument> {
    model.updatedDate = Date.now();
    return await this.readModel
      .update({ code: model.code }, model)
      .then((response) => {
        console.log("update Lead by code at query side");
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async checkDuplicate(query): Promise<boolean> {
    return await this.readModel
      .aggregate([{ $match: { name: query.name } }])
      .allowDiskUse(true)
      .exec()
      .then((result) => {
        if (result && result.length > 0) {
          return true;
        }
        return false;
      });
  }

  async checkDuplicateUpdate(query): Promise<boolean> {
    return await this.readModel
      .aggregate([
        {
          $match: {
            $and: [{ name: query.name }, { id: { $ne: query.id } }],
          },
        },
      ])
      .allowDiskUse(true)
      .exec()
      .then((result) => {
        if (result && result.length > 0) {
          return true;
        }
        return false;
      })
      .catch((error) => {
        throw error;
      });
  }

  async assignLeadForEmployee(model) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: {
            processBy: model.processBy,
            timeOut: model.timeOut,
            serverTime: model.serverTime,
            utcTime: model.utcTime,
          },
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  // Update status
  async updateStatus(model) {
    return await this.readModel
      .updateMany(
        { id: { $in: model.ids } },
        { $set: { status: model.status } },
        { multi: true }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async findLeadsProcessBy(loggedUser, type) {
    const match: any = {};
    if (type) {
      match.type = type;
    }

    // get employee
    const emp = await this.employeeRepository.findOne({
      "account.id": loggedUser.id,
    });
    if (!emp) {
      return [];
    }
    const employeeLogIn = emp[0];
    return await this.readModel
      .aggregate([
        {
          $match: {
            match,
            "lead.processBy": {
              $exists: true,
              $nin: [null, ""],
              $in: employeeLogIn.staffIds,
            },
          },
        },
        {
          $lookup: {
            from: 'employees',
            let: { processBy: '$processBy' },
            pipeline: [
              {
                $match:
                {
                  $expr:
                  {
                    $and:
                      [
                        { $in: ['$$processBy', '$staffIds'] },
                        { $eq: [loggedUser.id, '$id'] },
                      ]
                  }
                }
              },
            ],
            as: 'data'
          }
        },
        { $unwind: '$data' },

        {
          $project: {
            status: '$$ROOT.status',
            description: '$$ROOT.description',
            active: '$$ROOT.active',
            modifiedBy: '$$ROOT.modifiedBy',
            employeeTakeCare: '$$ROOT.employeeTakeCare',
            demandCustomer: '$$ROOT.demandCustomer',
            pos: '$$ROOT.pos',
            ticketId: '$$ROOT.ticketId',
            resource: '$$ROOT.resource',
            id: '$$ROOT.id',
            createDate: '$$ROOT.createDate',
            // employee: '$data' // if you want to get employee
          }
        },
        { $sort: { createDate: 1 } },
        {
          $project: {
            info: "$$ROOT",
            isOwner: {
              $cond: {
                if: { $eq: [loggedUser.id, "$processBy"] },
                then: "owners",
                else: "others",
              },
            },
          },
        },
        {
          $group: {
            _id: "$isOwner",
            list: { $push: "$info" },
          },
        },
      ])
      .allowDiskUse(true)
      .exec()
      .then((rs) => {
        const result = {};
        rs.forEach((e) => {
          result[e._id] = e.list;
        });
        return result;
      })
      .catch((err) => {
        return err;
      });
  }

  /**
   *
   * @param loggedUser user
   * @param {other || own }processType
   * @param query
   */
  async listLeadsProcessBy(loggedUser: any, query: any) {
    const processType = query.processType || "other";
    const limit = Number(query.pageSize) || 10;
    const skip = (Number(query.page) || 1) * limit - limit;
    const q: any = {
      processBy:
        processType === "other"
          ? { $nin: [loggedUser.id, null] }
          : loggedUser.id,
      lifeCycleStatus: {
        $nin: [
          LifeCycleStatusEnum.REMOVED,
          LifeCycleStatusEnum.PRIMARY_PROCESSING,
        ],
      },
      timeOut: { $gt: Date.now() },
    };
    if (query.type) {
      q.type = query.type;
    } else {
      q.type = { $ne: CommonConst.TYPE.PRIMARY };
    }
    let sort: any = {
      createdDate: -1,
    };
    if (!_.isEmpty(query.sort)) {
      sort = this.transformSort(query.sort);
    }
    return await this.readModel
      .find(q)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .exec();
  }

  async count(query: any = {}) {
    return this.readModel.countDocuments(query).exec();
  }

  async isValidBeforePull(employeeId, type: string) {
    const match: any = {
      processBy: employeeId,
      lifeCycleStatus: {
        $nin: [
          LifeCycleStatusEnum.PENDING,
          LifeCycleStatusEnum.REMOVED,
          LifeCycleStatusEnum.COMPLETED,
          LifeCycleStatusEnum.PRIMARY_PROCESSING,
        ],
      },
      timeOut: { $gt: Date.now() },
    };
    if (type) {
      match.type = type;
    } else {
      match.type = { $ne: CommonConst.TYPE.PRIMARY };
    }
    return await this.readModel
      .find(match)
      .exec()
      .then((rs) => {
        if (rs && rs.length > 0) return false;
        return true;
      })
      .catch((err) => {
        return err;
      });
  }

  async findLeadsToAssign(
    employeeId: string,
    posId: string,
    exchangeId: string,
    type: string,
    shared: boolean = false
  ) {
    const match: any = {
      processBy: null,
      $or: [{ "pos.id": posId }, { "pos.id": exchangeId }],
      // 'processedHistory.processBy': { $nin: [employeeId] },
      // 'processedHistory.isReject': { $ne: true },
      lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
    };
    if (shared) {
      match.$or.push({ "pos.id": "dxs-shared" });
    }
    if (type) {
      match.type = type;
    } else {
      match.type = { $ne: CommonConst.TYPE.ADVISING };
    }

    return await this.readModel
      .aggregate([
        {
          $match: match,
        },
        { $sort: { createdDate: 1 } },
        { $limit: 1 }, // Update: chỉ cho phép xử lý yêu cầu mỗi lần lấy
      ])
      .allowDiskUse(true)
      .exec()
      .then((rs) => {
        return rs;
      })
      .catch((err) => {
        return err;
      });
  }

  async processingLead(id: String) {
    let lifeCycleStatus = LifeCycleStatusEnum.PROCESSING;
    const model = await this.readModel.findOne({ id });
    if (model.type === CommonConst.TYPE.PRIMARY) {
      lifeCycleStatus = LifeCycleStatusEnum.PRIMARY_PROCESSING;
    }
    return await this.readModel
      .updateOne(
        { id },
        {
          $set: {
            updatedDate: Date.now(),
            lifeCycleStatus,
          },
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async unprocessingLead(model) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: {
            updatedDate: Date.now(),
            lifeCycleStatus: LifeCycleStatusEnum.ASSIGNED,
            note: model.note,
          },
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async delete(id: string) {
    return await this.readModel
      .deleteOne({ id })
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async deleteMany() {
    return await this.readModel
      .deleteMany({})
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async assignLead(model) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: model,
          $inc: { countAssign: 1 },
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async isReadyToAssign(leadId: string) {
    return await this.readModel
      .find({ id: leadId, processBy: null })
      .exec()
      .then((result) => {
        if (isNullOrUndefined(result)) return false;

        return true;
      });
  }

  async isValidMarkProcessing(userId: string, leadId) {
    return await this.readModel
      .find({
        processBy: userId,
        lifeCycleStatus: LifeCycleStatusEnum.PROCESSING,
        id: leadId,
      })
      .exec()
      .then((result) => {
        if (isNullOrUndefined(result) || result.length === 0) return true;
        return false;
      });
  }

  // Reassign
  async findLeadsAssignedEmployee(employeeId: string) {
    return await this.readModel
      .find({ processBy: employeeId })
      .exec()
      .then((rs) => {
        return rs;
      })
      .catch((err) => {
        return err;
      });
  }

  async findLeadsAssigned() {
    return await this.readModel
      .find({
        processBy: { $ne: null },
        lifeCycleStatus: {
          $nin: [
            LifeCycleStatusEnum.REMOVED,
            LifeCycleStatusEnum.PRIMARY_PROCESSING,
          ],
        },
      })
      .exec()
      .then((rs) => {
        return rs;
      })
      .catch((err) => {
        return err;
      });
  }

  async reassignLead(model: any) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: {
            updatedDate: Date.now(),
            modifiedBy: model.modifiedBy,
            processBy: model.processBy,
            timeOut: null,
            lifeCycleStatus: model.lifeCycleStatus,
            pos: model.pos,
            processedHistory: model.processedHistory,
          },
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async failLead(model: any) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: {
            updatedDate: Date.now(),
            modifiedBy: model.modifiedBy,
            processBy: model.processBy,
            timeOut: null,
            lifeCycleStatus: model.lifeCycleStatus,
            pos: model.pos,
            processedHistory: model.processedHistory,
          },
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async updateOne(model: any) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: model,
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async expiredLead(model: any) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: {
            updatedDate: Date.now(),
            modifiedBy: model.modifiedBy,
            processBy: model.processBy,
            timeOut: null,
            lifeCycleStatus: model.lifeCycleStatus,
            processedHistory: model.processedHistory,
          },
          $inc: { countAssign: 1 },
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  // Pending
  async pendingLead(model) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: {
            updatedDate: Date.now(),
            lifeCycleStatus: LifeCycleStatusEnum.PENDING,
            note: model.note,
          },
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async countLifeCycleStatusByEmployee(
    employeeId: String,
    lifeCycleStatus: string
  ) {
    return await this.readModel
      .find({
        processBy: employeeId,
        lifeCycleStatus,
      })
      .countDocuments()
      .then((rs) => {
        return rs;
      })
      .catch((ex) => {
        return ex;
      });
  }

  async countLifeCycleStatusForLeads(leadIds, lifeCycleStatus) {
    return await this.readModel
      .find({
        id: { $in: leadIds },
        lifeCycleStatus,
      })
      .countDocuments()
      .then((rs) => {
        return rs;
      })
      .catch((ex) => {
        return ex;
      });
  }

  async countAllTickets() {
    return this.readModel
      .countDocuments()
      .exec()
      .then((result) => {
        return { totalTickets: result };
      });
  }

  async countProcessTicketsDemandByUser(query) {
    return await this.readModel
      .find({
        $and: [
          query,
          {
            $or: [{ type: "BUY" }, { type: "RENT" }],
          },
        ],
      })
      .countDocuments()
      .exec()
      .then((demand) => {
        return demand;
      });
  }

  async countProcessTicketsConsignmentByUser(query) {
    return await this.readModel
      .find({
        $and: [
          query,
          {
            $or: [{ type: "SELL" }, { type: "LEASE" }],
          },
        ],
      })
      .countDocuments()
      .exec()
      .then((consignment) => {
        return consignment;
      });
  }

  async countLeadsProcessBy(loggedUser: any, query: any = {}): Promise<number> {
    const processType = query.processType || "other";
    const q = {
      processBy:
        processType === "other"
          ? { $nin: [loggedUser.id, null] }
          : loggedUser.id,
      lifeCycleStatus: { $ne: LifeCycleStatusEnum.REMOVED },
      timeOut: { $gt: Date.now() },
    };
    return await this.readModel.countDocuments(q).exec();
  }


  /**
   * Lấy danh sách yêu cầu tư vấn dịch vụ
   * @param page
   * @param pageSize
   * @param query
   */
  async listAllAdvising(
    page: number = 1,
    pageSize: number = 10,
    query
  ): Promise<ILeadDocument[]> {
    let sort: any = {
      createdDate: -1,
    };
    if (!_.isEmpty(query.sort)) {
      sort = this.transformSort(query.sort) || {
        createdDate: -1,
      };
    }
    return await this.readModel
      .aggregate([
        {
          $lookup: {
            from: "employees",
            localField: "processBy",
            foreignField: "id",
            as: "employeeTakeCare",
          },
        },
        {
          $match: {
            type: CommonConst.TYPE.ADVISING,
          },
        },
        { $sort: sort },
        { $skip: page * pageSize - pageSize },
        { $limit: pageSize },
      ])
      .allowDiskUse(true)
      .exec();
  }

  /**
   *
   * @param {Array<String>} customerId
   */
  findLeadByCustomerId(customerId: string[] = [], mapping: boolean = false) {
    return this.readModel
      .find({
        customerId: { $in: customerId, $exists: true, $nin: [null, ""] },
      })
      .exec()
      .then((res) => {
        if (mapping) {
          return _.groupBy(res, "customerId");
        }
        return res;
      });
  }

  protected transformSort(paramSort?: String) {
    let sort: any = paramSort;
    if (_.isString(sort)) {
      sort = sort.split(",");
    }
    if (Array.isArray(sort)) {
      let sortObj = {};
      sort.forEach((s) => {
        if (s.startsWith("-")) sortObj[s.slice(1)] = -1;
        else sortObj[s] = 1;
      });
      return sortObj;
    }

    return sort;
  }

  async filter(query: any = {}, isPaging: boolean = false) {
    const limit = parseInt(query["pageSize"]) || 10;
    const skip = (parseInt(query["page"]) || 1) * limit - limit;
    const { page, pageSize, ...q } = query;
    let sort: any = {
      createdDate: -1,
    };
    if (!_.isEmpty(query.sort)) {
      sort = this.transformSort(query.sort);
    }
    const aggregate = [
      {
        $match: q,
      },
      {
        $lookup: {
          from: "customers",
          localField: "customerId", // field in the orders collection
          foreignField: "id", // field in the items collection
          as: "customers",
        },
      },
      {
        $lookup: {
          from: "employees",
          localField: "processBy", // field in the orders collection
          foreignField: "id", // field in the items collection
          as: "employees",
        },
      },
      {
        $addFields: {
          customer: {
            $ifNull: ["$customer", { $arrayElemAt: ["$customers", 0] }],
          },
          employeeTakeCare: { $arrayElemAt: ["$employees", 0] },
        },
      },
      {
        $project: {
          customers: 0,
          employees: 0,
        },
      },
    ];
    if (isPaging) {
      return await this.readModel
        .aggregate(aggregate)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec();
    } else {
      return await this.readModel.aggregate(aggregate).sort(sort).exec();
    }
  }

  async filterReport(
    user: any = {},
    query: any = {},
    posInPool: any[] = [],
    isPaging: boolean = false
  ) {
    const limit = parseInt(query["pageSize"]) || 10;
    const skip = (parseInt(query["page"]) || 1) * limit - limit;
    const { page, pageSize, sort = "", ...q } = query;
    let sortObject: any = {
      createdDate: -1,
    };
    if (!_.isEmpty(query.sort)) {
      sortObject = this.transformSort(query.sort);
    }
    const aggregate = [
      {
        $match: {
          // type: {
          //   $in: [
          //     TransactionTypeEnum.buy,
          //     TransactionTypeEnum.lease,
          //     TransactionTypeEnum.rent,
          //     TransactionTypeEnum.sell,
          //   ],
          // },
          lifeCycleStatus: {
            $in: [LifeCycleStatusEnum.ASSIGNED, LifeCycleStatusEnum.PROCESSING, LifeCycleStatusEnum.PRIMARY_PROCESSING],
          },
          processBy: { $exists: true, $nin: [null, ""] },
        },
      },
      // {
      //   $lookup: {
      //     from: "customers",
      //     let: { customerId: "$customerId" },
      //     pipeline: [
      //       {
      //         $match: {
      //           $expr: {
      //             $and: [{ $eq: ["$$customerId", "$id"] }],
      //           },
      //         },
      //       },
      //       {
      //         $addFields: {
      //           "personalInfo.phone": {
      //             $cond: {
      //               if: { $ne: ["$modifiedBy", user.id] },
      //               then: "******",
      //               else: "$personalInfo.phone",
      //             },
      //           },
      //           phone: {
      //             $cond: {
      //               if: { $ne: ["$modifiedBy", user.id] },
      //               then: "******",
      //               else: "$phone",
      //             },
      //           },
      //         },
      //       },
      //     ],
      //     as: "customers",
      //   },
      // },
      // {
      //   $lookup: {
      //     from: "employees",
      //     let: { employeeId: "$processBy" },
      //     pipeline: [
      //       {
      //         $match: {
      //           $expr: {
      //             $and: [
      //               { $in: ["$$employeeId", "$staffIds"] },
      //               { $eq: [user.id, "$id"] },
      //             ],
      //           },
      //         },
      //       },
      //     ],
      //     as: "employeeTakeCares",
      //   },
      // },
      // { $unwind: "$employeeTakeCares" },
      // {
      //   $lookup: {
      //     from: "employees",
      //     let: { employeeId: "$processBy" },
      //     pipeline: [
      //       {
      //         $match: {
      //           $expr: {
      //             $and: [{ $eq: ["$$employeeId", "$id"] }],
      //           },
      //         },
      //       },
      //     ],
      //     as: "employeeTakeCare",
      //   },
      // },
      // { $unwind: "$employeeTakeCare" },
      // {
      //   $addFields: {
      //     customer: {
      //       $ifNull: ["$customer", { $arrayElemAt: ["$customers", 0] }],
      //     },
      //   },
      // },
      {
        $project: {
          customers: 0,
          employees: 0,
          phone: 0,
          employeeTakeCaress: 0,
          employeeTakeCares: 0,
        },
      },
      // {
      //   $match: q,
      // },
    ];
    if (isPaging) {
      return await this.readModel
        .aggregate(aggregate)
        .sort(sortObject)
        .skip(skip)
        .limit(limit)
        .allowDiskUse(true)
        .exec();
    } else {
      return await this.readModel
        .aggregate(aggregate)
        .sort(sortObject)
        .allowDiskUse(true)
        .exec();
    }
  }

  countReport(user: any = {}, query: any = {}, posInPool: string[] = []) {
    const { page, pageSize, ...q } = query;
    const aggregate = [
      {
        $match: {
          type: {
            $in: [
              TransactionTypeEnum.buy,
              TransactionTypeEnum.lease,
              TransactionTypeEnum.rent,
              TransactionTypeEnum.sell,
            ],
          },
          pos: { $exists: true, $nin: [null, ""] },
          $or: [
            {
              lifeCycleStatus: {
                $in: [
                  LifeCycleStatusEnum.ASSIGNED,
                  LifeCycleStatusEnum.PROCESSING,
                ],
              },
              processBy: { $exists: true, $nin: [null, ""] },
            },
            {
              lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
              "pos.id": { $exists: true, $nin: [null, ""], $in: posInPool },
            },
          ],
        },
      },
      {
        $lookup: {
          from: "customers",
          let: { customerId: "$customerId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ["$$customerId", "$id"] }],
                },
              },
            },
          ],
          as: "customers",
        },
      },
      {
        $lookup: {
          from: "employees",
          let: { employeeId: "$processBy", posId: "$pos.id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $in: ["$$employeeId", "$staffIds"] },
                    { $eq: [user.id, "$id"] },
                  ],
                },
              },
            },
          ],
          as: "employeeTakeCares",
        },
      },
      { $unwind: "$employeeTakeCares" },
      {
        $lookup: {
          from: "employees",
          let: { employeeId: "$processBy" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ["$$employeeId", "$id"] }],
                },
              },
            },
          ],
          as: "employeeTakeCare",
        },
      },
      { $unwind: "$employeeTakeCare" },
      {
        $addFields: {
          customer: {
            $ifNull: ["$customer", { $arrayElemAt: ["$customers", 0] }],
          },
        },
      },
      {
        $project: {
          customers: 0,
          employees: 0,
          phone: 0,
        },
      },
      {
        $match: q,
      },
    ];
    return this.readModel.aggregate(aggregate).allowDiskUse(true).exec();
  }

  countReportInPool(user: any = {}, query: any = {}, posInPool: string[] = []) {
    const { page, pageSize, ...q } = query;
    const aggregate = [
      {
        $match: {
          type: {
            $in: [
              TransactionTypeEnum.buy,
              TransactionTypeEnum.lease,
              TransactionTypeEnum.rent,
              TransactionTypeEnum.sell,
            ],
          },
          pos: { $exists: true, $nin: [null, ""] },
          $or: [
            {
              lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
              "pos.id": { $exists: true, $nin: [null, ""], $in: posInPool },
            },
          ],
        },
      },
      {
        $lookup: {
          from: "customers",
          let: { customerId: "$customerId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ["$$customerId", "$id"] }],
                },
              },
            },
          ],
          as: "customers",
        },
      },
      {
        $project: {
          customers: 0,
          employees: 0,
          phone: 0,
        },
      },
      {
        $match: q,
      },
    ];
    return this.readModel.aggregate(aggregate).allowDiskUse(true).exec();
  }

  async countLeadReadyForAssigningByPOS(posId: string, type: string) {
    const match: any = {
      processBy: null,
      "pos.id": posId,
      lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
    };
    if (type) {
      match.type = type;
    } else {
      match.type = { $ne: CommonConst.TYPE.ADVISING };
    }
    return await this.readModel
      .find(match)
      .countDocuments()
      .then((rs) => {
        return rs;
      })
      .catch((ex) => {
        return ex;
      });
  }

  async countLeadReadyForAssigningByEmp(employee: any, type: string) {
    const match: any = {
      processBy: null,
      $or: [{ "pos.id": employee.pos.id }, { "pos.id": employee.pos.parentId }],
      lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
    };
    if (employee.pos.taxNumber === "3602545493") {
      // Hard code DXS Tax No to get lead from shared pool
      match.$or.push({ "pos.id": "dxs-shared" });
    }
    if (type) {
      match.type = type;
    } else {
      match.type = { $ne: CommonConst.TYPE.ADVISING };
    }
    return await this.readModel
      .find(match)
      .countDocuments()
      .then((rs) => {
        return rs;
      })
      .catch((ex) => {
        return ex;
      });
  }

  findLeadByEmployeeId(employeeId: string[] = []) {
    return this.readModel
      .find({
        processBy: { $in: employeeId, $exists: true, $nin: [null, ""] },
        type: {
          $in: [
            TransactionTypeEnum.buy,
            TransactionTypeEnum.lease,
            TransactionTypeEnum.rent,
            TransactionTypeEnum.sell,
          ],
        },
      })
      .exec();
  }

  async countPrimaryLead(user: any, query: any) {
    const page: number = parseInt(query["page"]) || 1;
    const pageSize: number = parseInt(query["pageSize"]) || 10;
    const aggregate: any = [];
    const match: any = {};

    match.$and = [];
    match.$and.push({ type: CommonConst.TYPE.PRIMARY });
    // const emp = await this.employeeRepository.findOne({
    //   "account.id": user.id,
    // });
    // const employeeLogIn = emp[0];

    // if (!checkPermission(user.roles, PermissionConst.LEAD_REPORT_VIEW_ALL)) {
    //   if (employeeLogIn && employeeLogIn.id) {
    //     if (checkPermission(user.roles, PermissionConst.LEAD_REPORT_BY_EMP)) {
    //       match.$and.push(
    //         { processBy: { $nin: [null, ""] } },
    //         { processBy: { $in: employeeLogIn.staffIds } }
    //       );
    //     } else if (
    //       checkPermission(user.roles, PermissionConst.LEAD_REPORT_BY_IMPORTER)
    //     ) {
    //       match.$and.push({ "importedBy.id": employeeLogIn.id });
    //     } else {
    //       return [];
    //     }
    //   } else {
    //     return [];
    //   }
    // } else {
    //   match.$and.push({ "importedBy.id": { $in: employeeLogIn.staffIds } });
    // }

    match.$or = [
      { createdBy: user.id },
      { "importedBy.id": user.id }
    ];

    if (!_.isEmpty(query.posId)) {
      match["pos.id"] = query.posId;
    } else if (!_.isEmpty(query.exchangeId)) {
      match.$and.push({
        $or: [
          { "pos.id": query.exchangeId },
          { "pos.parentId": query.exchangeId },
        ],
      });
    }

    if (!_.isEmpty(query.assignedDateFrom)) {
      match.$and.push({
        assignedDate: { $gte: new Date(parseInt(query.assignedDateFrom, 10)) },
      });
    }

    if (!_.isEmpty(query.assignedDateTo)) {
      match.$and.push({
        assignedDate: { $lte: new Date(parseInt(query.assignedDateTo, 10)) },
      });
    }

    if (!_.isEmpty(query.resource)) {
      match.source = query.resource;
    } else {
      match.source = {
        $nin: [SourceEnum.DangTin, SourceEnum.YeuCau],
      };
    }

    if (!_.isEmpty(query["createdFrom"]) || !_.isEmpty(query["createdTo"])) {
      match["createdDate"] = {};
      if (!_.isEmpty(query["createdFrom"])) {
        match["createdDate"]["$gte"] = new Date(parseInt(query["createdFrom"]));
      }
      if (!_.isEmpty(query["createdTo"])) {
        match["createdDate"]["$lte"] = new Date(parseInt(query["createdTo"]));
      }
    }
    aggregate.push({
      $match: match,
    });
    aggregate.push({
      $addFields: {
        callHistoryCount: {
          $cond: [
            { $isArray: "$callHistory" },
            {
              $size: {
                $filter: {
                  input: "$callHistory",
                  as: "e",
                  cond: { $eq: ["$$e.isCalled", true] },
                },
              },
            },
            0,
          ],
        },
        callHistoryMinuteCount: {
          $cond: [
            { $isArray: "$callHistory" },
            {
              $sum: "$callHistory.answerTime",
            },
            0,
          ],
        },
      },
    });
    aggregate.push({
      $group: {
        _id: "$pos.id",
        posName: { $first: "$pos.name" },
        countAll: { $sum: 1 },
        countProcessing: {
          $sum: {
            $cond: [{ $eq: ["$lifeCycleStatus", "primary_processing"] }, 1, 0],
          },
        },
        countRemoved: {
          $sum: { $cond: [{ $eq: ["$lifeCycleStatus", "removed"] }, 1, 0] },
        },
        countInValid: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ["$lifeCycleStatus", "primary_processing"] },
                  { $eq: ["$updatedName", ""] },
                  { $eq: ["$updatedEmail", ""] },
                  { $eq: ["$updatedPhone", ""] },
                  { $eq: ["$isInNeed", ""] },
                  { $eq: ["$otherReason", ""] },
                  { $eq: ["$note", ""] },
                  { $eq: ["$needLoan", false] },
                  { $eq: ["$isAppointment", false] },
                  { $eq: ["$isVisited", false] },
                ],
              },
              1,
              0,
            ],
          },
        },
        countCall: {
          $sum: "$callHistoryCount",
        },
        countCallMinute: {
          $sum: "$callHistoryMinuteCount",
        },
        latestUpdate: { $max: "$updatedDate" },
      },
    });
    aggregate.push({
      $facet: {
        rows: [
          { $skip: Math.floor(pageSize * page - pageSize) },
          { $limit: pageSize },
        ],
        totalCount: [
          {
            $count: "count",
          },
        ],
      },
    });

    return this.readModel.aggregate(aggregate).allowDiskUse(true).exec();
  }

  async getLeadByStatus(
    user: any,
    status: ExploitEnum,
    query: any,
    page: number,
    pageSize: number
  ): Promise<any> {
    const offset = page === 1 ? 0 : (page - 1) * pageSize;

    const sort = { updatedDate: -1 };
    const conditions: any = {
      exploitStatus:
        status === ExploitEnum.ASSIGN
          ? {
            $in: [
              ExploitEnum.ASSIGN,
              //  ExploitEnum.REASSIGN
            ]
          }
          : status,
    };
    const emp = await this.employeeRepository.findOne({
      "account.id": user.id,
    });
    const employeeLogIn = emp[0];

    // if (!checkPermission(user.roles, PermissionConst.LEAD_REPORT_VIEW_ALL)) {
    //   if (checkPermission(user.roles, PermissionConst.LEAD_REPORT_BY_EMP)) {
    //     conditions["takeCare.id"] = { $in: employeeLogIn.staffIds };
    //   } else if (
    //     checkPermission(user.roles, PermissionConst.LEAD_REPORT_BY_IMPORTER)
    //   ) {
    //     conditions["importedBy.id"] = user.id;
    //   } else {
    //     return [[], 0];
    //   }
    // } else {
    //   conditions["importedBy.id"] = { $in: employeeLogIn?.staffIds || [] };
    // }

    conditions.$or = [
      { createdBy: user.id },
      { "importedBy.id": user.id }
    ];

    if (query.search) {
      conditions["$or"] = [
        { "code": { $regex: query.search, $options: "i" } },
        { "name": { $regex: query.search, $options: "i" } },
      ];
    }

    if (query.leadRepository) conditions.repoId = query.leadRepository;
    if (query.leadRepositoryConfig) {
      if (query.leadRepositoryConfig === "hot") conditions.isHot = true;
      else conditions.repoConfigCode = query.leadRepositoryConfig;
    }
    if (query.pos) conditions["pos.id"] = query.pos;
    if (query.takeCareId) conditions["takeCare.id"] = query.takeCareId;

    let startDate = 0;
    let endDate = 0;
    if (query["startDate"] && query["endDate"]) {
      startDate = Number(new Date(query["startDate"]));
      endDate = Number(new Date(query["endDate"])) + 86399999;
      conditions.createdDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    } else if (query["startDate"]) {
      startDate = Number(new Date(query["startDate"]));
      conditions.createdDate = { $gte: new Date(startDate) };
    } else if (query["endDate"]) {
      endDate = Number(new Date(query["endDate"])) + 86399999;
      conditions.createdDate = { $lte: new Date(endDate) };
    }

    return Promise.all([
      this.readModel
        .aggregate([
          { $match: conditions },
          {
            $lookup: {
              from: "lead-repos",
              let: {
                repoId: "$repoId",
                repoConfigCode: "$repoConfigCode",
              },
              as: "repo",
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ["$id", "$$repoId"],
                    },
                  },
                },
                {
                  $project: {
                    configHot: 1,
                    configs: {
                      $filter: {
                        input: "$configs",
                        as: "configs",
                        cond: {
                          $eq: ["$$configs.code", "$$repoConfigCode"],
                        },
                      },
                    },
                  },
                },
              ],
            },
          },
          {
            $addFields: {
              // => Get 1st item of repositories array, cus aggregate $lookup return an array with just 1 item
              repo: {
                $arrayElemAt: ["$repo", 0],
              },
            },
          },
          {
            $addFields: {
              // => Collect lead config
              config: {
                $arrayElemAt: [
                  {
                    $cond: {
                      if: { $eq: ["$isHot", true] },
                      then: ["$repo.configHot"],
                      else: "$repo.configs",
                    },
                  },
                  0,
                ],
              },
            },
          },
          {
            $addFields: {
              // => Collect visiblePhone flag in lead config
              visiblePhone: "$config.visiblePhone",
            },
          },
          {
            $project: {
              _id: 1,
              id: 1,
              code: 1,
              name: 1,
              phone: 1,
              exploitStatus: 1,
              pos: 1,
              takeCare: 1,
              updatedDate: 1,
              visiblePhone: 1,
              importedBy: 1,
              customer: 1,
              modifiedBy: 1,
              createdDate: 1,
            },
          },
          { $sort: sort },
          { $skip: offset },
          { $limit: pageSize },
        ])
        .allowDiskUse(true)
        .exec(),

      this.readModel.countDocuments(conditions),
    ]);


  }

  async getLeadsByStatusForExport(user: any, status: ExploitEnum, query: any) {
    const leads = [];
    const limit = 10;
    let page = 1;
    let serial = 0;

    const sort = { updatedDate: -1 };
    const conditions: any = {
      exploitStatus:
        status === ExploitEnum.ASSIGN
          ? {
            $in: [
              ExploitEnum.ASSIGN,
              // ExploitEnum.REASSIGN
            ]
          }
          : status,
    };

    // const emp = await this.employeeRepository.findOne({
    //   "account.id": user.id,
    // });
    // const employeeLogIn = emp[0];
    // if (!checkPermission(user.roles, PermissionConst.LEAD_REPORT_VIEW_ALL)) {
    //   if (checkPermission(user.roles, PermissionConst.LEAD_REPORT_BY_EMP)) {
    //     conditions["takeCare.id"] = { $in: employeeLogIn.staffIds };
    //   } else if (
    //     checkPermission(user.roles, PermissionConst.LEAD_REPORT_BY_IMPORTER)
    //   ) {
    //     conditions["importedBy.id"] = user.id;
    //   } else {
    //     return [[], 0];
    //   }
    // } else {
    //   conditions["importedBy.id"] = { $in: employeeLogIn.staffIds };
    // }

    conditions.$or = [
      { createdBy: user.id },
      { "importedBy.id": user.id }
    ];

    if (query.leadRepository) conditions.repoId = query.leadRepository;
    if (query.leadRepositoryConfig) {
      if (query.leadRepositoryConfig === "hot") conditions.isHot = true;
      else conditions.repoConfigCode = query.leadRepositoryConfig;
    }
    if (query.pos) conditions["pos.id"] = query.pos;
    if (query.takeCareId) conditions["takeCare.id"] = query.takeCareId;

    let startDate = 0;
    let endDate = 0;
    if (query["startDate"] && query["endDate"]) {
      startDate = Number(new Date(query["startDate"]));
      endDate = Number(new Date(query["endDate"])) + 86399999;
      conditions.updatedDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    } else if (query["startDate"]) {
      startDate = Number(new Date(query["startDate"]));
      conditions.updatedDate = { $gte: new Date(startDate) };
    } else if (query["endDate"]) {
      endDate = Number(new Date(query["endDate"])) + 86399999;
      conditions.updatedDate = { $lte: new Date(endDate) };
    }

    const total = await this.readModel.countDocuments(conditions);
    const totalPages = Math.ceil(total / limit);

    while (page <= totalPages) {
      const offset = page === 1 ? 0 : (page - 1) * limit;

      leads.push(
        ...(await this.readModel
          .aggregate([
            { $match: conditions },
            {
              $lookup: {
                from: "lead-repos",
                let: {
                  repoId: "$repoId",
                  repoConfigCode: "$repoConfigCode",
                },
                as: "repo",
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$id", "$$repoId"],
                      },
                    },
                  },
                  {
                    $project: {
                      configHot: 1,
                      configs: {
                        $filter: {
                          input: "$configs",
                          as: "configs",
                          cond: {
                            $eq: ["$$configs.code", "$$repoConfigCode"],
                          },
                        },
                      },
                    },
                  },
                ],
              },
            },
            {
              $addFields: {
                // => Get 1st item of repositories array, cus aggregate $lookup return an array with just 1 item
                repo: {
                  $arrayElemAt: ["$repo", 0],
                },
              },
            },
            {
              $addFields: {
                // => Collect lead config
                config: {
                  $arrayElemAt: [
                    {
                      $cond: {
                        if: { $eq: ["$isHot", true] },
                        then: ["$repo.configHot"],
                        else: "$repo.configs",
                      },
                    },
                    0,
                  ],
                },
              },
            },
            {
              $addFields: {
                // => Collect visiblePhone flag in lead config
                visiblePhone: "$config.visiblePhone",
              },
            },
            {
              $project: {
                _id: 0,
                code: 1,
                name: 1,
                phone: 1,
                exploitStatus: 1,
                pos: "$pos.name",
                exploitStatusModifiedBy: 1,
                exploitStatusModifiedAt: 1,
                takeCare: "$takeCare.name",
                updatedDate: 1,
                surveys: 1,
                reason: 1,
                visiblePhone: 1,
                importedBy: 1,
                exploitHistory: 1,
                customer: 1,
                callHistory: 1,
              },
            },
            { $sort: sort },
            { $skip: offset },
            { $limit: limit },
          ])
          .allowDiskUse(true)
          .exec())
      );

      page++;
    }

    return leads.map((item) => {
      serial++;

      return {
        serial,
        ...item,
      };
    });
  }

  async createMany(records: ILead[]) {
    if (records.length) {
      const bulkData = records.map((item) => {
        if (typeof (item as any).toObject === "function") {
          item = (item as any).toObject();
        }
        return {
          updateOne: {
            filter: { id: item.id },
            update: {
              $set: { _id: item.id, updatedDate: new Date(), ...item },
            },
            upsert: true,
            setDefaultsOnInsert: true,
          },
        };
      });

      return this.readModel
        .bulkWrite(bulkData)
        .then((r) => {
          const { upsertedCount, modifiedCount } = r;
          console.log(`BulkWrite ${this.readModel.modelName} result `, {
            upsertedCount,
            modifiedCount,
          });
        })
        .catch((e) => console.error(e));
    }
  }

  async updateMany(records: any[]) {
    if (records.length) {
      const bulkData = records.map((item) => {
        const latestAssignHistory = item.latestAssignHistory;
        delete item.latestAssignHistory;
        return {
          updateOne: {
            filter: { id: item.id },
            update: {
              $set: {
                _id: item.id,
                updatedDate: new Date(),
                ...item,
              },
              $push: {
                exploitHistory: {
                  $each: [latestAssignHistory],
                  $slice: -50,
                },
              },
            },
            setDefaultsOnInsert: true,
          },
        };
      });

      return this.readModel
        .bulkWrite(bulkData)
        .then((r) => {
          const { upsertedCount, modifiedCount } = r;
          console.log(`BulkWrite ${this.readModel.modelName} result `, {
            upsertedCount,
            modifiedCount,
          });
        })
        .catch((e) => console.error(e));
    }
  }

  async findMany(filter, projection = {}, limit = null) {
    return this.readModel.find(filter, projection).limit(limit);
  }

  async findExploitLead(filter) {
    const agg = [];
    agg.push(
      {
        $lookup: {
          from: "lead-repos",
          localField: "repoId",
          foreignField: "id",
          as: "leadRepo",
        },
      },
      {
        $addFields: {
          repo: {
            id: { $arrayElemAt: ["$leadRepo.id", 0] },
            name: { $arrayElemAt: ["$leadRepo.name", 0] },
            code: { $arrayElemAt: ["$leadRepo.code", 0] },
            config: {
              $arrayElemAt: [
                {
                  $cond: {
                    if: { $eq: ["$isHot", true] },
                    then: [{ code: "", name: "" }],
                    else: {
                      $map: {
                        input: {
                          $filter: {
                            input: { $arrayElemAt: ["$leadRepo.configs", 0] },
                            as: "config",
                            cond: { $eq: ["$repoConfigCode", "$$config.code"] },
                          },
                        },
                        as: "config",
                        in: {
                          code: "$$config.code",
                          name: "$$config.name",
                          visiblePhone: "$$config.visiblePhone",
                        },
                      },
                    },
                  },
                },
                0,
              ],
            },
          },
        },
      }
    );
    agg.push({
      $project: {
        _id: 0,
        id: 1,
        active: 1,
        assignDuration: 1,
        assignedDate: 1,
        name: 1,
        code: 1,
        phone: 1,
        countAssign: 1,
        isHot: 1,
        expireTime: 1,
        exploitHistory: 1,
        exploitStatus: 1,
        repo: 1,
        takeCare: 1,
        importedBy: 1,
        createdDate: 1,
        createdBy: 1,
        updatedDate: 1,
        modifiedBy: 1,
      }
    });
    agg.push(
      {
        $match: filter
      }
    );

    return this.readModel.aggregate(agg).allowDiskUse(true).exec();
  }

  async countExploitationByEmployee(where: Record<string, any>) {
    return this.readModel.aggregate([
      {
        $match: {
          "takeCare.id": where.takeCareIds,
          exploitStatus: where.statuses,
          "exploitHistory.updatedAt": where.dateFilter,
        },
      },
      { $unwind: "$exploitHistory" },
      {
        $match: {
          "exploitHistory.status": where.statuses,
          "exploitHistory.updatedAt": where.dateFilter,
        },
      },
      {
        $group: {
          _id: {
            date: {
              $dateToString: {
                format: "%Y-%m-%d",
                date: "$exploitHistory.updatedAt",
              },
            },
            status: "$exploitHistory.status",
          },
          count: {
            $sum: 1,
          },
        },
      },
    ]);
  }

  async getTodayExploited(userId: string) {
    const where = {
      "takeCare.id": userId,
      "exploitHistory.takeCareId": userId,
      "exploitHistory.status": {
        $in: [
          ExploitEnum.ASSIGN,
          //  ExploitEnum.REASSIGN
        ],
      },
      "exploitHistory.updatedAt": {
        $gte: new Date(moment().startOf("day").valueOf()),
        $lte: new Date(moment().endOf("day").valueOf()),
      },
    };
    return this.readModel.find(where, {
      id: 1,
      exploitStatus: 1,
      exploitHistory: 1,
      assignDuration: 1,
    });
  }

  async fetchLeadsWithAggs(filters) {
    const aggs = [
      {
        $match: filters, // => Get leads by filters
      },
      {
        $lookup: {
          from: `${CommonConst.LEAD_REPO_COLLECTION}s`,
          let: {
            repoId: "$repoId",
            configCode: "$repoConfigCode",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$id", "$$repoId"],
                },
              },
            },
            {
              $unwind: "$configs",
            },
            {
              $match: {
                $expr: {
                  $eq: ["$configs.code", "$$configCode"],
                },
              },
            },
            {
              $project: {
                value: {
                  $cond: {
                    if: {
                      $eq: ["$isHot", true],
                    },
                    then: "$configHot.visiblePhone",
                    else: "$configs.visiblePhone",
                  },
                },
              },
            },
          ],
          as: "visiblePhone",
        },
      },
      {
        $unwind: {
          path: "$visiblePhone",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          id: 1,
          code: 1,
          createdDate: 1,
          name: 1,
          phone: 1,
          email: 1,
          profileUrl: 1,
          isHot: 1,
          visiblePhone: {
            $cond: {
              if: {
                $eq: [{ $ifNull: ["$visiblePhone.value", false] }, false],
              },
              then: false,
              else: "$visiblePhone.value",
            },
          },
          assignDuration: 1,
          exploitStatus: 1,
          latestAssignHistory: {
            $arrayElemAt: [
              {
                $filter: {
                  input: "$exploitHistory",
                  as: "item",
                  cond: {
                    $or: [
                      {
                        $eq: ["$$item.status", "assign"],
                      },
                      // {
                      //   $eq: ["$$item.status", "reassign"],
                      // },
                    ],
                  },
                },
              },
              -1,
            ],
          },
        },
      },
    ];

    return this.readModel.aggregate(aggs);
  }

}
