<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>Notification</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/models/leadRepo/model.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Optional</span>
                                <a href="#app">app</a>
                            </li>
                            <li>
                                    <span class="modifier">Optional</span>
                                <a href="#email">email</a>
                            </li>
                            <li>
                                    <span class="modifier">Optional</span>
                                <a href="#sms">sms</a>
                            </li>
                            <li>
                                    <span class="modifier">Optional</span>
                                <a href="#smsCus">smsCus</a>
                            </li>
                            <li>
                                    <span class="modifier">Optional</span>
                                <a href="#web">web</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="app"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Optional</span>
                            app</b>
                            <a href="#app"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../classes/NotificationInstance.html" target="_self" >NotificationInstance</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional({type: NotificationInstance})<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="74" class="link-to-prism">src/modules/shared/models/leadRepo/model.ts:74</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="email"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Optional</span>
                            email</b>
                            <a href="#email"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../classes/NotificationInstance.html" target="_self" >NotificationInstance</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional({type: NotificationInstance})<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="68" class="link-to-prism">src/modules/shared/models/leadRepo/model.ts:68</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="sms"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Optional</span>
                            sms</b>
                            <a href="#sms"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../classes/NotificationInstance.html" target="_self" >NotificationInstance</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional({type: NotificationInstance})<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="77" class="link-to-prism">src/modules/shared/models/leadRepo/model.ts:77</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="smsCus"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Optional</span>
                            smsCus</b>
                            <a href="#smsCus"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../classes/NotificationInstance.html" target="_self" >NotificationInstance</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional({type: NotificationInstance})<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="80" class="link-to-prism">src/modules/shared/models/leadRepo/model.ts:80</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="web"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Optional</span>
                            web</b>
                            <a href="#web"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../classes/NotificationInstance.html" target="_self" >NotificationInstance</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @ApiModelPropertyOptional({type: NotificationInstance})<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="71" class="link-to-prism">src/modules/shared/models/leadRepo/model.ts:71</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { ApiModelPropertyOptional } from &#x27;@nestjs/swagger&#x27;;
import { Type } from &#x27;class-transformer&#x27;;
import { IsNotEmpty, Min, ValidateNested } from &#x27;class-validator&#x27;;
import { BaseModel } from &#x27;../base/base.model&#x27;;

export class Project {
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    id: string;

    @ApiModelPropertyOptional()
    @IsNotEmpty()
    name: string;
}

export class Survey {
    @ApiModelPropertyOptional()
    name: string;

    @ApiModelPropertyOptional()
    value: string;

    @ApiModelPropertyOptional()
    code: string;

    @ApiModelPropertyOptional()
    type: string;
}

export class OrgChartQueueItem {
    id: string;
    employeeQueue?: Object[];
}

export class OrgChart {
    @ApiModelPropertyOptional()
    id: string;

    @ApiModelPropertyOptional()
    name: string;

    staffIds: string[];
}

export class DateRange {
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    from: string;

    @IsNotEmpty()
    @ApiModelPropertyOptional()
    to: string;
}

export class NotificationInstance {
    @ApiModelPropertyOptional()
    title: string;

    @ApiModelPropertyOptional()
    content: string;

    @ApiModelPropertyOptional()
    active: boolean;
}

export class Notification {
    @ApiModelPropertyOptional({ type: NotificationInstance })
    email?: NotificationInstance;

    @ApiModelPropertyOptional({ type: NotificationInstance })
    web?: NotificationInstance;

    @ApiModelPropertyOptional({ type: NotificationInstance })
    app?: NotificationInstance;

    @ApiModelPropertyOptional({ type: NotificationInstance })
    sms?: NotificationInstance;

    @ApiModelPropertyOptional({ type: NotificationInstance })
    smsCus?: NotificationInstance;
}

export class LeadRepoConfigHot {
    @ApiModelPropertyOptional({ type: Notification })
    notification: Notification;

    @ApiModelPropertyOptional()
    @IsNotEmpty()
    orgChartIds: string[];

    @ApiModelPropertyOptional({ type: [OrgChart] })
    @ValidateNested({ each: true })
    @IsNotEmpty()
    @Type(() &#x3D;&gt; OrgChart)
    orgCharts: OrgChart[];

    @ApiModelPropertyOptional()
    @Min(0)
    assignDuration: number;

    @ApiModelPropertyOptional()
    visiblePhone?: boolean;

    orgChartQueue?: OrgChartQueueItem[];
    manualDeliver: boolean;
    isWorkingTime: boolean;
    workingTime: any[];
}

export class LeadRepoConfig extends LeadRepoConfigHot {
    @ApiModelPropertyOptional()
    @IsNotEmpty()
    projectId: string;

    @ApiModelPropertyOptional({ type: Project })
    @ValidateNested({ each: true })
    @IsNotEmpty()
    @Type(() &#x3D;&gt; Project)
    project: Project;

    @ApiModelPropertyOptional({ type: DateRange })
    @ValidateNested({ each: true })
    @Type(() &#x3D;&gt; DateRange)
    exploitTime: DateRange;

    code: string;

    @ApiModelPropertyOptional()
    @IsNotEmpty()
    name: string;

    @ApiModelPropertyOptional()
    active: boolean;

    @ApiModelPropertyOptional({ type: [Survey] })
    surveys: Survey[];
}

export class LeadRepo extends BaseModel {
    code: string;
    name: string;
    configHot: LeadRepoConfigHot;
    configs: LeadRepoConfig[];
}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'Notification.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
