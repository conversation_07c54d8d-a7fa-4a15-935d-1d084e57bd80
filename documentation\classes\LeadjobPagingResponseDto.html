<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>LeadjobPagingResponseDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/leadJob/web/dto/response.dto.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#page">page</a>
                            </li>
                            <li>
                                <a href="#pageSize">pageSize</a>
                            </li>
                            <li>
                                <a href="#rows">rows</a>
                            </li>
                            <li>
                                <a href="#total">total</a>
                            </li>
                            <li>
                                <a href="#totalPages">totalPages</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(init?: <a href="../classes/LeadjobPagingResponseDto.html">Partial<LeadjobPagingResponseDto></a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="11" class="link-to-prism">src/modules/leadJob/web/dto/response.dto.ts:11</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>init</td>
                                                  
                                                        <td>
                                                                        <code><a href="../classes/LeadjobPagingResponseDto.html" target="_self" >Partial&lt;LeadjobPagingResponseDto&gt;</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            Yes
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="page"></a>
                        <span class="name">
                            <b>
                            page</b>
                            <a href="#page"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="9" class="link-to-prism">src/modules/leadJob/web/dto/response.dto.ts:9</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="pageSize"></a>
                        <span class="name">
                            <b>
                            pageSize</b>
                            <a href="#pageSize"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="10" class="link-to-prism">src/modules/leadJob/web/dto/response.dto.ts:10</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="rows"></a>
                        <span class="name">
                            <b>
                            rows</b>
                            <a href="#rows"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../classes/LeadjobResponseDto.html" target="_self" >LeadjobResponseDto[]</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="7" class="link-to-prism">src/modules/leadJob/web/dto/response.dto.ts:7</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="total"></a>
                        <span class="name">
                            <b>
                            total</b>
                            <a href="#total"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="8" class="link-to-prism">src/modules/leadJob/web/dto/response.dto.ts:8</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="totalPages"></a>
                        <span class="name">
                            <b>
                            totalPages</b>
                            <a href="#totalPages"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="11" class="link-to-prism">src/modules/leadJob/web/dto/response.dto.ts:11</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { pick } from &quot;lodash&quot;;
import { ExploitHistory } from &quot;src/modules/lead.domain/dto/lead.dto&quot;;
import { ExploitCareEnum } from &quot;src/modules/shared/enum/exploit.enum&quot;;
import { IExploitHistory, ILeadjob, IProject, ITakeCare } from &quot;../../interfaces/base.interface&quot;;

export class LeadjobPagingResponseDto {
  rows: LeadjobResponseDto[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;

  constructor(init?: Partial&lt;LeadjobPagingResponseDto&gt;) {
    Object.assign(
      this,
      pick(init, [&quot;rows&quot;, &quot;total&quot;, &quot;page&quot;, &quot;pageSize&quot;, &quot;totalPages&quot;])
    );
  }
}

export class LeadjobResponseDto {
    customerId: string;
    id: string;
    title: string;
    name: string;
    address: string;
    phone: string;
    status: string;
    processBy: string;
    type: string;
    description: string;
    createdDate: Date;
    updatedDate: Date;
    email: string;
    notes: Object[];
    timestamp: number;
    customer: object;
    property: object;
    processedDate: Date;
    processedHistory: any[];
    code: string;
    assignedDate: Date;
    // new
    note: string;
    importedBy: Object;
    // LeadCare repository feature &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;
    exploitStatus: ExploitCareEnum;
    exploitStatusModifiedBy: string;
    repoId: string;
    takeCare: ITakeCare;
    project: IProject;
    leadCareId:string;
    block:string;
    isMonthly:boolean;
    dateEndWork:Date;
    timeStartWork: Date;
    timeEndWork: Date;
    customData: Object;
    idRepoConfig:string;
    nameRepoConfig:string;
    repoCode:string;
  constructor(init?: Partial&lt;LeadjobResponseDto | ILeadjob&gt;) {
    Object.assign(
      this,
      pick(init, [
        &quot;id&quot;,
        &quot;createdBy&quot;,
        &quot;modifiedDate&quot;,
        &quot;modifiedBy&quot;,
        &quot;title&quot;,
        // Custom fields
        &quot;customerId&quot;,
        &quot;name&quot;,
        &quot;address&quot;,
        &quot;phone&quot;,
        &quot;description&quot;,
        &quot;createdDate&quot;,
        &quot;updatedDate&quot;,
        &quot;email&quot;,
        &quot;notes&quot;,
        &quot;customer&quot;,
        &quot;processedDate&quot;,
        &quot;code&quot;,
        &quot;assignedDate&quot;,
        &quot;importedBy&quot;,
        // LeadCare repository feature &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;
        &quot;exploitStatus&quot;,
        &quot;takeCare&quot;,
        &quot;project&quot;,
        &quot;note&quot;,
        &quot;leadCareId&quot;,
        &quot;block&quot;,
        &quot;isMonthly&quot;,
        &quot;dateEndWork&quot;,
        &#x27;timeStartWork&#x27;,
        &#x27;timeEndWork&#x27;,
        &#x27;customData&#x27;,
        &#x27;repoCode&#x27;,
        &#x27;idRepoConfig&#x27;,
        &#x27;nameRepoConfig&#x27;,
        &#x27;implementMaxDate&#x27;,
        &#x27;isFirstWorkId&#x27;,
        &#x27;reason&#x27;,
      ])
    );
  }
}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadjobPagingResponseDto.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
