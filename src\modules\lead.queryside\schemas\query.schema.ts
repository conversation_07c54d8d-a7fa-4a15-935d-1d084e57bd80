import * as mongoose from 'mongoose';
import uuid = require('uuid');
import { StatusEnum } from '../../shared/enum/status.enum';
import { LifeCycleStatusEnum } from '../../shared/enum/life-cycle-status.enum';
import { ExploitEnum } from '../../shared/enum/exploit.enum';
import { CommonConst } from '../../shared/constant/common.const';
import { ILeadDocument } from '../interfaces/document.interface';

const categorySchema = new mongoose.Schema({
  id: { type: String },
  name: { type: String },
});
const addressSchema = new mongoose.Schema({
  nation: { type: String },
  province: { type: String },
  district: { type: String },
  ward: { type: String },
});

export const ValueSchema = new mongoose.Schema(
  {
    name: { type: String },
    code: { type: String },
    value: { type: Boolean, default: false },
  },
  { _id: false }
);

export const SurveySchema = new mongoose.Schema(
  {
    type: { type: String },
    name: { type: String },
    values: { type: [ValueSchema] },
    text: { type: String },
    multilineText: { type: String },
  },
  { _id: false }
);

const identifySchema = new mongoose.Schema({
  type: { type: String },
  num: { type: String },
  date: { type: String },
  issueBy: { type: String },
});

export const TakeCareSchema = new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  email: { type: String },
  phone: { type: String },
}, { _id: false });

export const ExploitHistorySchema = new mongoose.Schema({
  status: { type: String },
  updatedAt: { type: Date },
  updatedBy: { type: String },
  takeCareId: { type: String },
  takeCareInfo: TakeCareSchema,
}, { _id: false });

export const QuerySchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },
  email: { type: String },
  name: { type: String },
  profileUrl: { type: String },
  address: { type: String },
  phone: { type: String },
  pos: { type: Object },
  type: { type: String, default: CommonConst.TYPE.PRIMARY, index: true },
  status: { type: String, default: StatusEnum.GREEN },
  lifeCycleStatus: { type: String, default: LifeCycleStatusEnum.IN_POOL },
  processBy: { type: String, default: null },
  t0: { type: Date },
  t1: { type: Date },
  t2: { type: Date },
  t3: { type: Date },
  timeOut: { type: Date, default: null },
  timezoneclient: { type: String },
  notes: { type: Object },
  customerId: { type: String },

  createdDate: { type: Date, default: () => Date.now(), index: true  },
  updatedDate: { type: Date, default: () => Date.now(), index: true  },
  description: { type: String, default: '' },
  active: { type: Boolean, default: true },
  createdBy: { type: String, default: null, index: true },
  modifiedBy: { type: String, default: null },
  source: { type: String, default: null, index: true },
  code: { type: String },
  demandCustomer: { type: Object },
  employee: { type: Object },
  property: { type: Object },
  processedDate: { type: Date },
  assignedDate: { type: Date },
  processedHistory: { type: Array },
  isCalled: { type: Boolean, default: false },
  images: { type: Object },
  advisingType: { type: String },
  price: { type: Number },
  categoryId: { type: String },
  desirablePrice: { type: String },
  category: { type: categorySchema },
  usedFloorArea: { type: Number },

  updatedName: { type: String },
  updatedPhone: { type: String },
  updatedEmail: { type: String },
  updatedProfileUrl: { type: String },

  isInNeed: { type: String },
  reasonNoNeed: { type: String },
  otherReason: { type: String },
  reason: { type: [String] },
  interestedProduct: { type: Object },
  direction: { type: Object },
  needLoan: { type: Boolean, default: false },
  isAppointment: { type: Boolean, default: false },
  isVisited: { type: Boolean, default: false },
  note: { type: String },
  callHistory: { type: Array },
  importedBy: { type: Object, index: true },

  sex: { type: String },
  subPhone: { type: [Array], default: [] },
  objAddress: { type: addressSchema, default: {} },
  needPersons: { type: String },
  identification: { type: identifySchema },
  incomePerMonth: { type: Number, default: 0 },
  sourceIncome: { type: String },
  dob: { type: String },
  major: { type: String },
  maritalStatus: { type: String },
  surveys: { type: [SurveySchema], default: [] },
  orgCode: { type: String, index: true },
  orgName: { type: String, index: true },

  // Lead Repository + Automatic delivery
  exploitStatus: { type: String, default: ExploitEnum.NEW },
  exploitStatusModifiedBy: { type: Object },
  takeCare: TakeCareSchema,
  exploitHistory: [ExploitHistorySchema],
  assignDuration: { type: Number },
  countAssign: { type: Number, default: 0 },
  repoId: { type: String, index: true },
  repoConfigCode: { type: String, index: true },
  isHot: { type: Boolean, default: false },
  project: { type: Object, default: {} },
  expireTime: { type: Date, index: true },
  notiUser: { type: Object },
});

QuerySchema.pre('save', function (next) {
  this._id = this.get('id');
  next();
});
