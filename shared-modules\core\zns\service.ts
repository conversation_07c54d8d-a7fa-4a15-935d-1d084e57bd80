import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { znsDto } from './zns.dto';

@Injectable()
export class ZnsService {
  private readonly context = ZnsService.name;
  constructor(
    private readonly configService: ConfigService,
  ) { }
  async send(payload: znsDto) {
    const apiUrl = this.configService.get('ZNS_URL');
    try {
      const response = await axios.post(apiUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        auth: {
          username: this.configService.get('ZNS_BASIC_AUTH_USERNAME') || "",
          password: this.configService.get('ZNS_BASIC_AUTH_PASSWORD') || "",
        },
      });

      return response.data;
    } catch (error) {
      const status = error.response?.status || 500;
      const description =
        error.response?.data?.description ||
        error.response?.data?.message ||
        'Lỗi hệ thống - G<PERSON>i ZNS thất bại';
      return {
        status,
        description,
      };
    }

  }
}
