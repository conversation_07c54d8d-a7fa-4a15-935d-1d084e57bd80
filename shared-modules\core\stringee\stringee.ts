import {
  Injectable
} from '@nestjs/common';
import { AxiosRequestConfig } from 'axios';
import * as jwt from 'jsonwebtoken';
import { GetCallLogsDto } from './stringee.dto';
import { StringeeUrl } from './stringee.url';

@Injectable()
export class Stringee {
  async callHistory(data: GetCallLogsDto, httpService): Promise<any> {
    const url = StringeeUrl.CALL_LOG;
    try {
      const config: AxiosRequestConfig = {
        params: data
      }
      var token = await this.getAccessToken();
      const response = await this.proxyCallStringee(null, url, token, 'get', config, httpService)
      return response?.data?.data;
    } catch (error) {
      return error;
    }
  }

  async getAccessToken(): Promise<string> {
    //Lưu ý add key trong env
    const apiKeySid = process.env.STRINGEE_API_KEY_SID; 
    const apiKeySecret = process.env.STRINGEE_API_KEY_SECRET;

    let now = Math.floor(Date.now() / 1000);
    let exp = now + 3600;

    let header = {cty: "stringee-api;v=1"};
    let payload = {
      jti: apiKeySid + "-" + now,
      iss: apiKeySid,
      exp: exp,
      rest_api: true
    };

    return new Promise((resolve, reject) => {
      jwt.sign(payload, apiKeySecret, { algorithm: 'HS256', header }, (err, token) => {
        if (err) {
          reject(err);
        } else {
          resolve(token);
        }
      });
    });
  }

  async proxyCallStringee<T>(requestBody: T, url: string, token: string | null = null, methodRequest: string, config?: AxiosRequestConfig, httpService?): Promise<any> {
    const requestConfig: AxiosRequestConfig = {
      method: methodRequest,
      url: url,
      headers: {
        'Content-Type': 'application/json',
        'X-STRINGEE-AUTH': token
      },
      ...config,
    };
  
    if (requestBody)
      requestConfig.data = JSON.stringify(requestBody);
    else
      requestConfig.data = {}
  
    try {
      //Lưu ý httpService được truyền vào phải là HttpService của thư viện @nestjs/axios, 
      //trường hợp không còn phù hợp thì xem xét viết lại chỗ này.
      const response = await httpService.request(requestConfig).toPromise();
      return response;
    } catch (error) {
      // Log the error 
      console.error(error);
    }
  }
}
