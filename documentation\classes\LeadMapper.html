<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>LeadMapper</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/mapper/lead.mapper.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#leadResponse">leadResponse</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#timezoneclient">timezoneclient</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#convertDate">convertDate</a>
                            </li>
                            <li>
                                <a href="#enitiesToDtos">enitiesToDtos</a>
                            </li>
                            <li>
                                <a href="#enityToDto">enityToDto</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(timezoneclient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="9" class="link-to-prism">src/modules/shared/mapper/lead.mapper.ts:9</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>timezoneclient</td>
                                                  
                                                        <td>
                                                                        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="leadResponse"></a>
                        <span class="name">
                            <b>
                            leadResponse</b>
                            <a href="#leadResponse"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
        &#x27;id&#x27;: &#x27;id&#x27;,
        &#x27;name&#x27;: &#x27;name&#x27;,
        &quot;address&quot;: &quot;address&quot;,
        &quot;phone&quot;: &quot;phone&quot;,
        &quot;email&quot;: &quot;email&quot;,
        &quot;profileUrl&quot;: &quot;profileUrl&quot;,
        &quot;pos&quot;: {
            key: &quot;pos&quot;,
            transform: function (value) {
                if (isNullOrUndefined(value)) return;
                return { id: value.id, name: value.name, parentId: value.parentId };
            }
        },
        &quot;status&quot;: [
            { key: &quot;status&quot; },
            {
                key: &quot;periodStatus&quot;,
                transform: (value &#x3D;&gt; {
                    let rs &#x3D; &#x27;&#x27;;
                    if (isNullOrUndefined(value)) return;
                    switch (value) {
                        case StatusEnum.GREEN:
                            rs &#x3D; &#x27;Mới&#x27;;
                            break;
                        case StatusEnum.YELLOW:
                            rs &#x3D; &#x27;Chờ&#x27;;
                            break;
                        case StatusEnum.RED:
                            rs &#x3D; &#x27;Ưu tiên&#x27;;
                            break;
                        default:
                            break;
                    }
                    return rs;
                })
            }],
        &quot;lifeCycleStatus&quot;: &quot;lifeCycleStatus&quot;,
        &quot;type&quot;: &quot;type&quot;,
        &quot;timezoneclient&quot;: &quot;timezoneClient&quot;,
        &quot;createdDate&quot;: {
            key: &quot;createdDate&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;updatedDate&quot;: {
            key: &quot;updatedDate&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;processedTicketDate&quot;: {
            key: &quot;processedTicketDate&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;assignedDate&quot;: {
            key: &quot;assignedDate&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;t0&quot;: {
            key: &quot;t0&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;t1&quot;: {
            key: &quot;t1&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;t2&quot;: {
            key: &quot;t2&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;t3&quot;: {
            key: &quot;t3&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;timeOut&quot;: {
            key: &quot;timeOut&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;notes&quot;: &quot;notes&quot;,
        &quot;description&quot;: &quot;description&quot;,
        &quot;reason&quot;: &quot;reason&quot;,
        &quot;decisionDate&quot;: &quot;decisionDate&quot;,
        &quot;customerId&quot;: &quot;customerId&quot;,
        &quot;source&quot;: &quot;source&quot;,
        &quot;code&quot;: &quot;code&quot;,
        &quot;customer&quot;: &quot;customer&quot;,
        &quot;employee&quot;: &quot;employee&quot;,
        &quot;property&quot;: &quot;property&quot;,
        &quot;surveys&quot;: &quot;surveys&quot;,
        &quot;processedDate&quot;: &quot;processedDate&quot;,
        &quot;processBy&quot;: &quot;processBy&quot;,
        &quot;employeeTakeCare&quot;: &quot;employeeTakeCare&quot;,
        &quot;processedTicketCode&quot;: &quot;processedTicketCode&quot;,
        &quot;isCalled&quot;: &quot;isCalled&quot;,
        &#x27;advisingType&#x27;: &#x27;advisingType&#x27;,
        &#x27;images&#x27;: &#x27;images&#x27;,
        &#x27;price&#x27;: &#x27;price&#x27;,
        &#x27;categoryId&#x27;: &#x27;categoryId&#x27;,
        &#x27;configData&#x27;: &#x27;configData&#x27;,
        &#x27;desirablePrice&#x27;: &#x27;desirablePrice&#x27;,
        &#x27;category&#x27;: &#x27;category&#x27;,
        &#x27;updatedName&#x27;: &#x27;updatedName&#x27;,
        &#x27;updatedPhone&#x27;:&#x27;updatedPhone&#x27;,
        &#x27;updatedEmail&#x27;:&#x27;updatedEmail&#x27;,
        &#x27;updatedProfileUrl&#x27;:&#x27;updatedProfileUrl&#x27;,
        &#x27;isInNeed&#x27;: &#x27;isInNeed&#x27;,
        &#x27;reasonNoNeed&#x27;: &#x27;reasonNoNeed&#x27;,
        &#x27;otherReason&#x27;: &#x27;otherReason&#x27;,
        &#x27;interestedProduct&#x27;: &#x27;interestedProduct&#x27;,
        &#x27;direction&#x27;: &#x27;direction&#x27;,
        &#x27;needLoan&#x27;: &#x27;needLoan&#x27;,
        &#x27;isAppointment&#x27;: &#x27;isAppointment&#x27;,
        &#x27;isVisited&#x27;: &#x27;isVisited&#x27;,
        &#x27;note&#x27;: &#x27;note&#x27;,
        &#x27;callHistory&#x27;: &#x27;callHistory&#x27;,
        &#x27;callHistoryCount&#x27;: &#x27;callHistoryCount&#x27;,
        &#x27;callHistoryMinuteCount&#x27;: &#x27;callHistoryMinuteCount&#x27;,
        &#x27;importedBy&#x27;: &#x27;importedBy&#x27;,
        &#x27;exploitStatus&#x27;: &#x27;exploitStatus&#x27;,
        exploitHistory: &#x27;exploitHistory&#x27;,
        project: &#x27;project&#x27;,
        isHot: &#x27;isHot&#x27;,
        visiblePhone: &#x27;visiblePhone&#x27;
    }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="14" class="link-to-prism">src/modules/shared/mapper/lead.mapper.ts:14</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="timezoneclient"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                            timezoneclient</b>
                            <a href="#timezoneclient"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="9" class="link-to-prism">src/modules/shared/mapper/lead.mapper.ts:9</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="convertDate"></a>
                    <span class="name">
                        <b>
                            convertDate
                        </b>
                        <a href="#convertDate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>convertDate(value)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="137"
                            class="link-to-prism">src/modules/shared/mapper/lead.mapper.ts:137</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>value</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="enitiesToDtos"></a>
                    <span class="name">
                        <b>
                            enitiesToDtos
                        </b>
                        <a href="#enitiesToDtos"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>enitiesToDtos(entities)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="145"
                            class="link-to-prism">src/modules/shared/mapper/lead.mapper.ts:145</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>entities</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="enityToDto"></a>
                    <span class="name">
                        <b>
                            enityToDto
                        </b>
                        <a href="#enityToDto"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>enityToDto(entity)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="141"
                            class="link-to-prism">src/modules/shared/mapper/lead.mapper.ts:141</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>entity</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { isNullOrUndefined } from &quot;util&quot;;
import { StatusEnum } from &quot;../enum/status.enum&quot;;
import { ILead } from &quot;../services/lead/interfaces/lead.interface&quot;;

const moment &#x3D; require(&#x27;moment-timezone&#x27;);
const objectMapper &#x3D; require(&#x27;object-mapper&#x27;);

export class LeadMapper {
    private timezoneclient: string;
    constructor(timezoneclient: string) {
        this.timezoneclient &#x3D; timezoneclient;
        process.env.timezone &#x3D; timezoneclient;
    }
    leadResponse &#x3D; {
        &#x27;id&#x27;: &#x27;id&#x27;,
        &#x27;name&#x27;: &#x27;name&#x27;,
        &quot;address&quot;: &quot;address&quot;,
        &quot;phone&quot;: &quot;phone&quot;,
        &quot;email&quot;: &quot;email&quot;,
        &quot;profileUrl&quot;: &quot;profileUrl&quot;,
        &quot;pos&quot;: {
            key: &quot;pos&quot;,
            transform: function (value) {
                if (isNullOrUndefined(value)) return;
                return { id: value.id, name: value.name, parentId: value.parentId };
            }
        },
        &quot;status&quot;: [
            { key: &quot;status&quot; },
            {
                key: &quot;periodStatus&quot;,
                transform: (value &#x3D;&gt; {
                    let rs &#x3D; &#x27;&#x27;;
                    if (isNullOrUndefined(value)) return;
                    switch (value) {
                        case StatusEnum.GREEN:
                            rs &#x3D; &#x27;Mới&#x27;;
                            break;
                        case StatusEnum.YELLOW:
                            rs &#x3D; &#x27;Chờ&#x27;;
                            break;
                        case StatusEnum.RED:
                            rs &#x3D; &#x27;Ưu tiên&#x27;;
                            break;
                        default:
                            break;
                    }
                    return rs;
                })
            }],
        &quot;lifeCycleStatus&quot;: &quot;lifeCycleStatus&quot;,
        &quot;type&quot;: &quot;type&quot;,
        &quot;timezoneclient&quot;: &quot;timezoneClient&quot;,
        &quot;createdDate&quot;: {
            key: &quot;createdDate&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;updatedDate&quot;: {
            key: &quot;updatedDate&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;processedTicketDate&quot;: {
            key: &quot;processedTicketDate&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;assignedDate&quot;: {
            key: &quot;assignedDate&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;t0&quot;: {
            key: &quot;t0&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;t1&quot;: {
            key: &quot;t1&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;t2&quot;: {
            key: &quot;t2&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;t3&quot;: {
            key: &quot;t3&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;timeOut&quot;: {
            key: &quot;timeOut&quot;,
            transform: (value &#x3D;&gt; this.convertDate(value))
        },
        &quot;notes&quot;: &quot;notes&quot;,
        &quot;description&quot;: &quot;description&quot;,
        &quot;reason&quot;: &quot;reason&quot;,
        &quot;decisionDate&quot;: &quot;decisionDate&quot;,
        &quot;customerId&quot;: &quot;customerId&quot;,
        &quot;source&quot;: &quot;source&quot;,
        &quot;code&quot;: &quot;code&quot;,
        &quot;customer&quot;: &quot;customer&quot;,
        &quot;employee&quot;: &quot;employee&quot;,
        &quot;property&quot;: &quot;property&quot;,
        &quot;surveys&quot;: &quot;surveys&quot;,
        &quot;processedDate&quot;: &quot;processedDate&quot;,
        &quot;processBy&quot;: &quot;processBy&quot;,
        &quot;employeeTakeCare&quot;: &quot;employeeTakeCare&quot;,
        &quot;processedTicketCode&quot;: &quot;processedTicketCode&quot;,
        &quot;isCalled&quot;: &quot;isCalled&quot;,
        &#x27;advisingType&#x27;: &#x27;advisingType&#x27;,
        &#x27;images&#x27;: &#x27;images&#x27;,
        &#x27;price&#x27;: &#x27;price&#x27;,
        &#x27;categoryId&#x27;: &#x27;categoryId&#x27;,
        &#x27;configData&#x27;: &#x27;configData&#x27;,
        &#x27;desirablePrice&#x27;: &#x27;desirablePrice&#x27;,
        &#x27;category&#x27;: &#x27;category&#x27;,
        &#x27;updatedName&#x27;: &#x27;updatedName&#x27;,
        &#x27;updatedPhone&#x27;:&#x27;updatedPhone&#x27;,
        &#x27;updatedEmail&#x27;:&#x27;updatedEmail&#x27;,
        &#x27;updatedProfileUrl&#x27;:&#x27;updatedProfileUrl&#x27;,
        &#x27;isInNeed&#x27;: &#x27;isInNeed&#x27;,
        &#x27;reasonNoNeed&#x27;: &#x27;reasonNoNeed&#x27;,
        &#x27;otherReason&#x27;: &#x27;otherReason&#x27;,
        &#x27;interestedProduct&#x27;: &#x27;interestedProduct&#x27;,
        &#x27;direction&#x27;: &#x27;direction&#x27;,
        &#x27;needLoan&#x27;: &#x27;needLoan&#x27;,
        &#x27;isAppointment&#x27;: &#x27;isAppointment&#x27;,
        &#x27;isVisited&#x27;: &#x27;isVisited&#x27;,
        &#x27;note&#x27;: &#x27;note&#x27;,
        &#x27;callHistory&#x27;: &#x27;callHistory&#x27;,
        &#x27;callHistoryCount&#x27;: &#x27;callHistoryCount&#x27;,
        &#x27;callHistoryMinuteCount&#x27;: &#x27;callHistoryMinuteCount&#x27;,
        &#x27;importedBy&#x27;: &#x27;importedBy&#x27;,
        &#x27;exploitStatus&#x27;: &#x27;exploitStatus&#x27;,
        exploitHistory: &#x27;exploitHistory&#x27;,
        project: &#x27;project&#x27;,
        isHot: &#x27;isHot&#x27;,
        visiblePhone: &#x27;visiblePhone&#x27;
    }

    convertDate(value) {
        if (isNullOrUndefined(value)) return;
        return moment(value).tz(this.timezoneclient).format();
    }
    enityToDto(entity) {
        return objectMapper(entity, this.leadResponse);
    }

    enitiesToDtos(entities) {
        let dtos &#x3D; [];
        if (isNullOrUndefined(entities)) {
            return;
        }

        entities.forEach(entity &#x3D;&gt; {
            dtos.push(this.enityToDto(entity));
        });
        return dtos;
    }
}</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadMapper.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
