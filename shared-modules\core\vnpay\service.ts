import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
const moment = require('moment');
import { vnpayCreateUrlDto, vnpTransactionDto } from './vnpay.dto';

@Injectable()
export class VnPayService {
  private readonly context = VnPayService.name;
  constructor(
    private readonly configService: ConfigService,
  ) { }
  async createPaymentUrlVersion(body: vnpayCreateUrlDto, ipAddr: string) {
    var tmnCode = this.configService.get('VNP_TMNCODE');
    var secretKey = this.configService.get('VNP_HASHSECRET');
    var vnpUrl = this.configService.get('VNP_URL');
    var returnUrl = this.configService.get('VNP_RETURNURL');

    var date = new Date();
    let createDate = moment(date).format('YYYYMMDDHHmmss');
    let orderId = body.orderId || moment(date).format('DDHHmmss');

    var amount = body.amount;
    var bankCode = body.bankCode;

    var locale = body.language;
    if (!locale) {
      locale = 'vn';
    }
    var currCode = 'VND';
    var vnp_Params = {};
    vnp_Params['vnp_Version'] = '2.1.0';
    vnp_Params['vnp_Command'] = 'pay';
    vnp_Params['vnp_TmnCode'] = tmnCode;
    // vnp_Params['vnp_Merchant'] = ''
    vnp_Params['vnp_Locale'] = locale;
    vnp_Params['vnp_CurrCode'] = currCode;
    vnp_Params['vnp_TxnRef'] = orderId;
    vnp_Params['vnp_OrderInfo'] = 'Thanh toan cho ma GD:' + orderId;
    vnp_Params['vnp_OrderType'] = 'other';
    vnp_Params['vnp_Amount'] = amount * 100;
    vnp_Params['vnp_ReturnUrl'] = returnUrl;
    vnp_Params['vnp_IpAddr'] = ipAddr;
    vnp_Params['vnp_CreateDate'] = createDate;
    // vnp_Params['vnp_ExpireDate'] = moment(date).add(30, 'minutes').format('YYYYMMDDHHmmss');
    if (bankCode !== null && bankCode !== '') {
      vnp_Params['vnp_BankCode'] = bankCode;
    }

    vnp_Params = this.sortObject(vnp_Params);

    let querystring = require('qs');
    let signData = querystring.stringify(vnp_Params, { encode: false });
    let crypto = require("crypto");
    let hmac = crypto.createHmac("sha512", secretKey);
    const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest("hex");
    vnp_Params['vnp_SecureHash'] = signed;
    vnpUrl += '?' + querystring.stringify(vnp_Params, { encode: false });
    // create payment info
    // await this.paymentRepository.create({
    //     gatewayPaymentType: GatewayPaymentTypeEnum.VNPAY,
    //     data: vnp_Params,
    //     status: PaymentStatusEnum.UNPAID,
    //     transactionId: orderId,
    //     transaction: body
    // })
    return vnpUrl
  }
  
  async vnpTransactionVersion(body: vnpTransactionDto, ipAddr: string): Promise<any> {
    let date = new Date();

    var vnp_TmnCode = this.configService.get('VNP_TMNCODE');
    var secretKey = this.configService.get('VNP_HASHSECRET');
    var vnp_Api = this.configService.get('VNP_API');

    let vnp_IpAddr = ipAddr;
    let vnp_TxnRef = body.orderId;
    let vnp_TransactionDate = body.transDate || moment(date).format('YYYYMMDDHHmmss');;
    let vnp_RequestId = moment(date).format('HHmmss');
    let vnp_Version = '2.1.0';
    let vnp_Command = 'querydr';
    let vnp_OrderInfo = 'Truy van GD ma:' + vnp_TxnRef;

    let currCode = 'VND';
    let vnp_CreateDate = moment(date).format('YYYYMMDDHHmmss');

    let data = vnp_RequestId + "|" + vnp_Version + "|" + vnp_Command + "|" + vnp_TmnCode + "|" + vnp_TxnRef + "|" + vnp_TransactionDate + "|" + vnp_CreateDate + "|" + vnp_IpAddr + "|" + vnp_OrderInfo;
    let crypto = require("crypto");
    let hmac = crypto.createHmac("sha512", secretKey);
    const vnp_SecureHash = hmac.update(Buffer.from(data, 'utf-8')).digest("hex");

    let dataObj = {
      'vnp_RequestId': vnp_RequestId,
      'vnp_Version': vnp_Version,
      'vnp_Command': vnp_Command,
      'vnp_TmnCode': vnp_TmnCode,
      'vnp_TxnRef': vnp_TxnRef,
      'vnp_OrderInfo': vnp_OrderInfo,
      'vnp_TransactionDate': vnp_CreateDate,
      'vnp_CreateDate': vnp_CreateDate,
      'vnp_IpAddr': ipAddr,
      'vnp_SecureHash': vnp_SecureHash
    };
    // /merchant_webapi/api/transaction
    try {
      const response = await axios.post(vnp_Api, dataObj, {
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data; // Trả về phản hồi từ API VNPAY
    } catch (error) {
      console.error("Lỗi khi gọi API VNPAY:", error);
      throw new Error("Giao dịch thất bại, vui lòng thử lại sau.");
    }
  }
  sortObject(obj) {
    let sorted: any;
    let str: any;
    let key;
    for (key in obj) {
      if (obj.hasOwnProperty(key)) {
        str.push(encodeURIComponent(key));
      }
    }
    str.sort();
    for (key = 0; key < str.length; key++) {
      sorted[str[key]] = encodeURIComponent(obj[str[key]]).replace(/%20/g, "+");
    }
    return sorted;
  }
}
