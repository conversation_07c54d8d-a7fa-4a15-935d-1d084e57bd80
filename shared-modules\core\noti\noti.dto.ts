import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsArray,
  IsEmail,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class notificationPayloadDto {
  @IsString()
  @IsNotEmpty()
  domain: string; // Ví dụ: "CRM_1000"

  @IsBoolean()
  autoSendWhenFail: boolean; // Ví dụ: false

  @IsNumber()
  cancelSendDay: number; // Ví dụ: 0

  @IsString()
  @IsNotEmpty()
  sender: string; // Mặc định: "<EMAIL>"

  @IsString()
  @IsNotEmpty()
  subject: string; // Tiêu đề email

  @IsString()
  @IsNotEmpty()
  content: string; // Nội dung email (HTML)

  @IsArray()
  @IsOptional()
  receives: string[]; // Danh sách email nhận chính

  @IsArray()
  @IsOptional()
  ccReceives: string[]; // <PERSON>h sách <PERSON>

  @IsArray()
  @IsOptional()
  bccReceives: string[]; // Danh sách BCC
}

export class notificationDto {
  @IsOptional()
  @IsString()
  receiver: string;

  @IsString()
  @IsNotEmpty()
  type: string; // "email", "sms", "notification"

  @IsOptional()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  body: string;

  @ValidateNested()
  payload: any;

  @IsOptional()
  @IsString()
  extraInfo: string;

  @IsOptional()
  @IsString()
  system: string;

  @IsOptional()
  @IsBoolean()
  persistence: boolean; // Ví dụ: false
}
