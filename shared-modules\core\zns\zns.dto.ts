import {
  IsString,
  IsNotEmpty,
  IsOptional
} from 'class-validator';

export class znsDto {
  @IsString()
  @IsNotEmpty()
  client_req_id: string; // Mã yêu cầu client (VD: "2")

  @IsString()
  @IsNotEmpty()
  from: string; // ID Zalo OA gửi đi (VD: "1054399944397407937")

  @IsString()
  @IsNotEmpty()
  to: string; // SĐT người nhận (VD: "84376439136")

  @IsString()
  @IsNotEmpty()
  template_id: string; // ID của template ZNS đã được duy<PERSON>t (VD: "326326")

  @IsNotEmpty()
  template_data: any; // Dữ liệu động cho template (VD: { otp: "1234" })
}
