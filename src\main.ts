import { NestFactory } from "@nestjs/core";
import { ApplicationModule } from "./app.module";
import { Transport } from "@nestjs/microservices";
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger";
import * as bodyParser from "body-parser";
import * as cors from "cors";
import { ConfigService } from "@nestjs/config";
import * as fs from "fs";
import { join } from "path";
import express = require("express");
import https = require("https");
import http = require("http");
import { ExpressAdapter } from "@nestjs/platform-express";
import * as helmet from "helmet";
import * as filter from "content-filter";
import { HttpExceptionFilter, ResponseInterceptor, CustomValidationPipe, QueueConst } from "../shared-modules"

async function bootstrap() {
  // const tlsKey = join(__dirname, '..', 'certs', 'privateKey.pem');
  // const tlsCrt = join(__dirname, '..', 'certs', 'mycert.pem');

  // const httpsOptions = {
  //     key: fs.readFileSync(tlsKey),
  //     cert: fs.readFileSync(tlsCrt),
  // };

  const server = express();
  const app = await NestFactory.create(
    ApplicationModule,
    new ExpressAdapter(server)
  );

  const configService = app.get(ConfigService);

  // const configService = app.get(ConfigService);
  const apiPrefix = configService.get("API_PREFIX");
  const docsPrefix = configService.get("DOCS_PREFIX");

  app.setGlobalPrefix(apiPrefix);
  // app.use('/document', express.static(join(__dirname, '..', 'documentation'))); // document
  app.enableCors();
  app.use(
    bodyParser({ limit: configService.get("LIMIT_REQUEST_BODY") || "50mb" })
  );
  app.use(bodyParser.json());

  app.use(bodyParser.urlencoded({ extended: true }));
  app.use(helmet());
  app.use(
    filter({
      urlBlackList: [
        ".htaccess",
        ".htaccess.bak",
        ".htaccess~",
        "~root",
        "weblogic",
        "iisadmin",
        "iissamples",
        "index.jsp",
        "manager",
        "index.html~",
        "/.htaccess.bak",
        "htaccess.txt",
        "/.htaccess~",
        "/~root",
        "/weblogic",
        "/iisadmin",
        "/iissamples",
        "/index.jsp",
        "/manager",
        "/index.html~",
        "/.bash_history",
        "/.bashrc",
        "htpasswd.bak",
        ".htpasswd",
        ".htpasswd.old",
        ".htpasswd~",
        ".profile",
        "/.history",
        "/.mysql_history",
        "/perl",
        "/index.php",
        "/index.html.bak",
        "/index.html.old",
      ],
    })
  );

  app.use(
    filter({ urlMessage: "A forbidden expression has been found in URL: " })
  );

  app.connectMicroservice({
    transport: Transport.RMQ,
    options: {
      urls: [configService.get("RABBITMQ_URL")],
      queue: QueueConst.LEAD_QUEUE,
      queueOptions: { durable: false },
    },
  });

  app.useGlobalFilters(new HttpExceptionFilter());
  app.useGlobalInterceptors(new ResponseInterceptor());
  app.useGlobalPipes(new CustomValidationPipe({ whitelist: false }));

  // swagger
  const options = new DocumentBuilder()
    .setTitle("RP swagger")
    .setDescription("Welcome to RP API docs")
    .setVersion("1.0")
    .addBearerAuth()
    .setBasePath(apiPrefix)
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup(docsPrefix, app, document);

  await app.startAllMicroservicesAsync();

  const port = parseInt(configService.get("PORT"), 10);
  // await app.listen(port, () => console.log('Application is listening on port: ', port));

  await app.init();
  http
    .createServer(server)
    .listen(port, () =>
      console.log("Application is listening on port: ", port)
    );
  // if (process.env.NODE_ENV !== 'local')
  // https.createServer(httpsOptions, server).listen(443, () => console.log('Application is listening on port: ', 443));
}
bootstrap();
