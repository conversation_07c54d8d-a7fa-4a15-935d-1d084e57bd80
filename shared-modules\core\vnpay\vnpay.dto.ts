import {
  IsString,
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  Min,
  IsOptional
} from 'class-validator';

export class vnpayCreateUrlDto {
  @IsNotEmpty()
  @IsNumber()
  amount: number;//Số tiền cần thanh toán (VND)

  @IsString()
  @IsNotEmpty()
  orderId: string;//Mã đơn hàng nội bộ unique

  @IsString()
  @IsOptional()
  bankCode: string;//Mã ngân hàng (theo chuẩn VNPAY) Ví dụ: "NCB"

  @IsString()
  @IsOptional()
  language: string;
}

export class vnpTransactionDto {
  @IsString()
  @IsNotEmpty()
  orderId: string;  //Mã đơn hàng nội bộ unique

  @IsString()
  @IsOptional()
  transDate: string;//định dạng yyyyMMddHHmmss (Time zone GMT+7) Ví dụ: **************

}